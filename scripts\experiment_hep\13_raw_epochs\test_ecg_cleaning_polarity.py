#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ECG清洗过程对极性的影响
比较原始信号和清洗后信号的极性差异
"""

import sys
import os
import argparse
import logging
import time
import warnings
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import mne
import neurokit2 as nk
import h5py
from scipy import signal

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']

plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

# 数据路径配置
DATA_DIR = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-HBA\result\hep_analysis\14_rest_vs_test_analysis\hepdata"
PLOTS_DIR = os.path.join(OUTPUT_DIR, 'plots')
os.makedirs(PLOTS_DIR, exist_ok=True)

# ECG导联候选列表
ECG_CANDIDATES = ['ECG11', 'ECG7', 'ECG8', 'ECG12']

def setup_logging():
    """设置日志系统"""
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    logging.basicConfig(level=logging.INFO, format=log_format)
    return logging.getLogger(__name__)

def load_data(subject_id, stage_number, stage_type, logger):
    """加载指定被试和阶段的数据"""
    try:
        filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_{stage_type}_TP9TP10Ref.fif"
        file_path = os.path.join(DATA_DIR, filename)

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {filename}")
            return None

        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
        return raw

    except Exception as e:
        logger.error(f"加载数据失败 {filename}: {str(e)}")
        return None

def detect_ecg_polarity(ecg_signal, r_peaks, sampling_rate, window_ms=100):
    """检测ECG信号的R波极性"""
    window_samples = int(window_ms * sampling_rate / 1000)
    r_wave_amplitudes = []
    
    for peak in r_peaks[:min(10, len(r_peaks))]:  # 只分析前10个R峰
        start = max(0, peak - window_samples // 2)
        end = min(len(ecg_signal), peak + window_samples // 2)
        
        if start < end:
            local_signal = ecg_signal[start:end]
            baseline = np.median(local_signal)
            peak_value = ecg_signal[peak]
            r_wave_amplitudes.append(peak_value - baseline)
    
    if r_wave_amplitudes:
        avg_amplitude = np.mean(r_wave_amplitudes)
        return 'positive' if avg_amplitude > 0 else 'negative', avg_amplitude
    else:
        return 'positive', 0

def analyze_ecg_cleaning_effect(subject_id, logger):
    """分析ECG清洗对极性的影响"""
    try:
        logger.info(f"开始分析被试 {subject_id:02d} 的ECG清洗效果")
        
        # 加载数据
        raw = load_data(subject_id, '02', 'test', logger)
        if raw is None:
            return None
        
        # 分析所有ECG通道
        available_ecg = [ch for ch in ECG_CANDIDATES if ch in raw.ch_names]
        if not available_ecg:
            available_ecg = [ch for ch in raw.ch_names if ch.startswith('ECG')]
        
        results = {}
        
        for ch_name in available_ecg:
            try:
                # 获取原始信号
                ecg_raw = raw.get_data(picks=[ch_name])[0]
                
                # 清洗信号
                ecg_cleaned = nk.ecg_clean(ecg_raw, sampling_rate=raw.info['sfreq'])
                
                # 检测R峰（使用原始信号）
                try:
                    _, rpeaks_info_raw = nk.ecg_peaks(ecg_raw, sampling_rate=raw.info['sfreq'])
                    r_peaks_raw = rpeaks_info_raw.get('ECG_R_Peaks', [])
                except:
                    r_peaks_raw = []
                
                # 检测R峰（使用清洗后信号）
                try:
                    _, rpeaks_info_cleaned = nk.ecg_peaks(ecg_cleaned, sampling_rate=raw.info['sfreq'])
                    r_peaks_cleaned = rpeaks_info_cleaned.get('ECG_R_Peaks', [])
                except:
                    r_peaks_cleaned = []
                
                # 分析极性
                if len(r_peaks_raw) > 5:
                    polarity_raw, amplitude_raw = detect_ecg_polarity(ecg_raw, r_peaks_raw, raw.info['sfreq'])
                else:
                    polarity_raw, amplitude_raw = 'unknown', 0
                    
                if len(r_peaks_cleaned) > 5:
                    polarity_cleaned, amplitude_cleaned = detect_ecg_polarity(ecg_cleaned, r_peaks_cleaned, raw.info['sfreq'])
                else:
                    polarity_cleaned, amplitude_cleaned = 'unknown', 0
                
                results[ch_name] = {
                    'raw_signal': ecg_raw,
                    'cleaned_signal': ecg_cleaned,
                    'r_peaks_raw': r_peaks_raw,
                    'r_peaks_cleaned': r_peaks_cleaned,
                    'polarity_raw': polarity_raw,
                    'polarity_cleaned': polarity_cleaned,
                    'amplitude_raw': amplitude_raw,
                    'amplitude_cleaned': amplitude_cleaned,
                    'polarity_changed': polarity_raw != polarity_cleaned
                }
                
                logger.info(f"{ch_name}: 原始={polarity_raw}({amplitude_raw:.2f}), "
                           f"清洗后={polarity_cleaned}({amplitude_cleaned:.2f}), "
                           f"极性改变={polarity_raw != polarity_cleaned}")
                
            except Exception as e:
                logger.error(f"分析{ch_name}失败: {str(e)}")
                results[ch_name] = {'error': str(e)}
        
        # 创建对比可视化
        create_cleaning_comparison_plot(subject_id, results, logger)
        
        return results
        
    except Exception as e:
        logger.error(f"分析被试 {subject_id:02d} 失败: {str(e)}")
        return None

def create_cleaning_comparison_plot(subject_id, results, logger):
    """创建ECG清洗前后对比图"""
    try:
        # 找到第一个有效的通道
        valid_channel = None
        for ch_name, result in results.items():
            if 'error' not in result and len(result.get('r_peaks_raw', [])) > 5:
                valid_channel = ch_name
                break
        
        if valid_channel is None:
            logger.warning(f"被试 {subject_id:02d} 没有有效的ECG通道")
            return
        
        result = results[valid_channel]
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 10))
        
        # 选择显示的时间段（前5秒）
        display_samples = min(2500, len(result['raw_signal']))  # 5秒 * 500Hz
        times = np.arange(display_samples) / 500.0  # 转换为秒
        
        # 找到显示范围内的R峰
        display_r_peaks_raw = [p for p in result['r_peaks_raw'] if p < display_samples]
        display_r_peaks_cleaned = [p for p in result['r_peaks_cleaned'] if p < display_samples]
        
        # 原始信号
        axes[0, 0].plot(times, result['raw_signal'][:display_samples], 'b-', linewidth=1, label='原始ECG信号')
        if display_r_peaks_raw:
            axes[0, 0].scatter(np.array(display_r_peaks_raw)/500.0, 
                              result['raw_signal'][display_r_peaks_raw], 
                              color='red', s=50, zorder=5, label=f'R峰 ({result["polarity_raw"]})')
        axes[0, 0].set_title(f'被试 {subject_id:02d} - {valid_channel} 原始信号 (极性: {result["polarity_raw"]})', fontsize=12)
        axes[0, 0].set_ylabel('幅值', fontsize=10)
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].legend()
        
        # 清洗后信号
        axes[0, 1].plot(times, result['cleaned_signal'][:display_samples], 'g-', linewidth=1, label='清洗后ECG信号')
        if display_r_peaks_cleaned:
            axes[0, 1].scatter(np.array(display_r_peaks_cleaned)/500.0, 
                              result['cleaned_signal'][display_r_peaks_cleaned], 
                              color='red', s=50, zorder=5, label=f'R峰 ({result["polarity_cleaned"]})')
        axes[0, 1].set_title(f'被试 {subject_id:02d} - {valid_channel} 清洗后信号 (极性: {result["polarity_cleaned"]})', fontsize=12)
        axes[0, 1].set_ylabel('幅值', fontsize=10)
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].legend()
        
        # 极性对比（放大R波区域）
        if display_r_peaks_raw and display_r_peaks_cleaned:
            # 选择第一个R峰周围的信号
            peak_raw = display_r_peaks_raw[0]
            peak_cleaned = display_r_peaks_cleaned[0]
            
            window = 100  # ±100个采样点
            start_raw = max(0, peak_raw - window)
            end_raw = min(len(result['raw_signal']), peak_raw + window)
            start_cleaned = max(0, peak_cleaned - window)
            end_cleaned = min(len(result['cleaned_signal']), peak_cleaned + window)
            
            times_raw = (np.arange(start_raw, end_raw) - peak_raw) / 500.0 * 1000  # 转换为毫秒
            times_cleaned = (np.arange(start_cleaned, end_cleaned) - peak_cleaned) / 500.0 * 1000
            
            axes[1, 0].plot(times_raw, result['raw_signal'][start_raw:end_raw], 'b-', linewidth=2, label='原始信号')
            axes[1, 0].axvline(x=0, color='red', linestyle='--', alpha=0.7, label='R峰位置')
            axes[1, 0].set_title(f'原始信号R波细节 (幅值: {result["amplitude_raw"]:.2f})', fontsize=12)
            axes[1, 0].set_xlabel('时间 (ms)', fontsize=10)
            axes[1, 0].set_ylabel('幅值', fontsize=10)
            axes[1, 0].grid(True, alpha=0.3)
            axes[1, 0].legend()
            
            axes[1, 1].plot(times_cleaned, result['cleaned_signal'][start_cleaned:end_cleaned], 'g-', linewidth=2, label='清洗后信号')
            axes[1, 1].axvline(x=0, color='red', linestyle='--', alpha=0.7, label='R峰位置')
            axes[1, 1].set_title(f'清洗后信号R波细节 (幅值: {result["amplitude_cleaned"]:.2f})', fontsize=12)
            axes[1, 1].set_xlabel('时间 (ms)', fontsize=10)
            axes[1, 1].set_ylabel('幅值', fontsize=10)
            axes[1, 1].grid(True, alpha=0.3)
            axes[1, 1].legend()
        
        plt.tight_layout()
        
        # 保存图片
        filename = f"{subject_id:02d}_ecg_cleaning_polarity_comparison.png"
        filepath = os.path.join(PLOTS_DIR, filename)
        plt.savefig(filepath, dpi=100, bbox_inches='tight', facecolor='white')
        plt.close()
        
        logger.info(f"保存ECG清洗对比图片: {filename}")
        
    except Exception as e:
        logger.error(f"创建ECG清洗对比图失败: {str(e)}")

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始ECG清洗极性影响测试")
    
    # 测试被试（选择几个代表性的）
    test_subjects = [1, 15, 30, 5, 20]
    
    all_results = {}
    for subject_id in test_subjects:
        result = analyze_ecg_cleaning_effect(subject_id, logger)
        if result:
            all_results[subject_id] = result
    
    # 总结分析
    logger.info("\n=== ECG清洗极性影响总结 ===")
    polarity_changes = 0
    total_channels = 0
    
    for subject_id, subject_results in all_results.items():
        for ch_name, ch_result in subject_results.items():
            if 'error' not in ch_result:
                total_channels += 1
                if ch_result.get('polarity_changed', False):
                    polarity_changes += 1
                    logger.info(f"被试{subject_id} {ch_name}: 极性改变 "
                               f"{ch_result['polarity_raw']} -> {ch_result['polarity_cleaned']}")
    
    logger.info(f"总通道数: {total_channels}")
    logger.info(f"极性改变的通道数: {polarity_changes}")
    logger.info(f"极性改变比例: {polarity_changes/total_channels*100:.1f}%" if total_channels > 0 else "无有效数据")
    
    logger.info("ECG清洗极性影响测试完成!")

if __name__ == "__main__":
    main()
