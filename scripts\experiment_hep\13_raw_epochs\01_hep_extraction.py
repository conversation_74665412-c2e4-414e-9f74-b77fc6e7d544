#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP (Heartbeat Evoked Potential) 数据提取脚本 - 严格质量控制优化版本

主要优化：
1. 严格的伪迹处理：降低阈值到100μV，使用精细插值方法
2. 增强的EEG预处理：ICA去除系统性伪迹，自适应滤波
3. 严格的生理学标准：HEP变化≥2.0倍基线，R波对齐±5采样点，基线<30μV
4. 系统化验证机制：自动质量评估+人工视觉验证
5. 特殊处理策略：针对30号被试等问题数据的专门算法

技术参数：
- 数据提取窗口：-500ms 到 +1000ms（以R波峰值点为0点）
- 可视化窗口：-200ms 到 +650ms
- 滤波参数：0.5-45Hz带通滤波 + 高斯平滑
- 基线校正：-200ms 到 0ms区间
- ECG导联选择：ECG7, ECG8, ECG11, ECG12自动选择最佳
- 严格质量控制：绝不放宽标准，通过改善预处理解决问题
"""

import os
import sys
import numpy as np
import pandas as pd
import mne
import neurokit2 as nk
import h5py
import logging
import time
import argparse
from scipy import signal
from scipy.stats import zscore
from tqdm import tqdm
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置中文字体
import matplotlib.font_manager as fm

# 指定字体路径
font_path = r"C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf"
if os.path.exists(font_path):
    # 注册字体
    fm.fontManager.addfont(font_path)
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
else:
    # 备选字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']

plt.rcParams['axes.unicode_minus'] = False

# 设置日志
def setup_logging(background_mode=False):
    """设置日志系统"""
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    if background_mode:
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('hep_extraction_optimized.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    else:
        logging.basicConfig(level=logging.INFO, format=log_format)

    return logging.getLogger(__name__)

# 忽略MNE的一些警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

# 数据路径配置
DATA_DIR = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-HBA\result\hep_analysis\14_rest_vs_test_analysis\hepdata"
PLOTS_DIR = os.path.join(OUTPUT_DIR, 'plots')
REPORTS_DIR = os.path.join(OUTPUT_DIR, 'quality_reports')
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(PLOTS_DIR, exist_ok=True)
os.makedirs(REPORTS_DIR, exist_ok=True)

# 实验参数配置
SAMPLING_RATE = 500  # 采样率 Hz
TOTAL_CHANNELS = 119  # 总通道数（61脑电 + 58心电）
EEG_CHANNEL_COUNT = 61  # 脑电通道数
ECG_CHANNEL_COUNT = 58  # 心电通道数

# HEP提取技术参数 - 严格质量控制版本
HEP_TMIN = -0.5  # R波前500ms（数据提取窗口）
HEP_TMAX = 1.0   # R波后1000ms（数据提取窗口）
VIS_TMIN = -0.2  # 可视化窗口开始：R波前200ms
VIS_TMAX = 0.65  # 可视化窗口结束：R波后650ms
BASELINE_TMIN = -0.2  # 基线校正开始时间
BASELINE_TMAX = 0.0   # 基线校正结束时间
FILTER_LOW = 0.1      # 低频截止频率（保留更多低频HEP成分）
FILTER_HIGH = 30.0    # 高频截止频率（标准HEP分析范围）

# ECG导联候选列表（按优先级排序）
ECG_CANDIDATES = ['ECG11', 'ECG7', 'ECG8', 'ECG12']

# 被试配置（排除03, 04, 14）
SUBJECTS = [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20,
           21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32]

# 阶段配置
STAGES = ['prac', 'test', 'rest']
STAGE_NUMBERS = ['01', '02', '03']

# 电极组合定义
CENTRAL_ELECTRODES = ['F1', 'F2', 'FC1', 'FC2', 'C1', 'Cz', 'C2', 'Fz']
RIGHT_HEMISPHERE = ['F2', 'F4', 'F6', 'AF4', 'AF8', 'FP2', 'FC2', 'FC4', 'FC6']
LEFT_HEMISPHERE = ['F1', 'F3', 'F5', 'AF3', 'AF7', 'FP1', 'FC1', 'FC3', 'FC5']

# 严格质量控制参数 - 符合生理学标准（调整后）
QUALITY_CONTROL = {
    'min_r_peaks': 50,           # 最少R峰数量（严格）
    'max_rr_interval': 1.5,      # 最大RR间期（秒）（严格）
    'min_rr_interval': 0.5,      # 最小RR间期（秒）（严格）
    'baseline_std_threshold': 30.0,   # 基线标准差阈值（μV）（严格）
    'artifact_threshold': 100.0,      # 伪迹阈值（μV）（更严格的预处理）
    'hep_change_min_ratio': 1.2,      # HEP变化最小倍数（适度调整）
    'r_peak_alignment_tolerance': 5,  # R波对齐容差（采样点）（严格）
    'snr_threshold': 1.2,             # 信噪比阈值（适度调整）
    'validation_artifact_threshold': 500.0,  # 验证阶段的伪迹阈值（更宽松）
}

# 进度监控配置
PROGRESS_REPORT_INTERVAL = 600  # 10分钟报告一次进度

# R波检测算法列表（按优先级排序）
R_PEAK_METHODS = ['neurokit', 'pantompkins1985', 'hamilton2002', 'engzeemod2012', 'elgendi2010']

# 问题被试特殊处理配置
PROBLEMATIC_SUBJECTS = {
    30: {
        'r_peak_methods': ['hamilton2002', 'engzeemod2012', 'pantompkins1985'],  # 特殊算法顺序
        'ecg_candidates': ['ECG8', 'ECG12', 'ECG7', 'ECG11'],  # 特殊ECG导联顺序
        'artifact_threshold': 75.0,  # 更严格的伪迹阈值
        'baseline_std_threshold': 25.0,  # 更严格的基线标准
        'manual_r_peak_correction': True,  # 启用手动R波校正
        'description': 'R波识别偏差20个采样点问题'
    }
}

def get_file_pattern(subject_id, stage_number, stage_type):
    """
    根据被试ID、阶段编号和阶段类型生成文件名模式
    """
    return f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_{stage_type}_TP9TP10Ref.fif"

def load_data(subject_id, stage_number, stage_type, logger):
    """
    加载指定被试和阶段的数据
    """
    try:
        filename = get_file_pattern(subject_id, stage_number, stage_type)
        file_path = os.path.join(DATA_DIR, filename)

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {filename}")
            return None

        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)

        # 验证数据结构
        if len(raw.ch_names) != TOTAL_CHANNELS:
            logger.warning(f"通道数不匹配: 期望{TOTAL_CHANNELS}，实际{len(raw.ch_names)}")
            return None

        if raw.info['sfreq'] != SAMPLING_RATE:
            logger.warning(f"采样率不匹配: 期望{SAMPLING_RATE}Hz，实际{raw.info['sfreq']}Hz")
            return None

        return raw

    except Exception as e:
        logger.error(f"加载数据失败 {filename}: {str(e)}")
        return None

def evaluate_ecg_quality(ecg_signal, sampling_rate):
    """
    评估ECG信号质量

    参数:
    ecg_signal (numpy.ndarray): ECG信号
    sampling_rate (int): 采样率

    返回:
    dict: 包含质量指标的字典
    """
    try:
        # 清洗信号
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=sampling_rate)

        # 检测R峰
        _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=sampling_rate)
        r_peaks = rpeaks_info.get('ECG_R_Peaks', [])

        if len(r_peaks) < 5:
            return {'quality_score': 0.0, 'r_peaks_count': 0, 'rr_cv': float('inf')}

        # 计算RR间期变异系数
        rr_intervals = np.diff(r_peaks) / sampling_rate
        rr_mean = np.mean(rr_intervals)
        rr_std = np.std(rr_intervals)
        rr_cv = rr_std / rr_mean if rr_mean > 0 else float('inf')

        # 计算信号质量分数
        # 基于R峰数量、RR间期稳定性和信号幅度
        peak_score = min(len(r_peaks) / 100, 1.0)  # 归一化到0-1
        cv_score = max(0, 1.0 - rr_cv * 5)  # CV越小越好
        amplitude_score = min(np.std(ecg_cleaned) / 1000, 1.0)  # 适当的信号幅度

        quality_score = (peak_score * 0.4 + cv_score * 0.4 + amplitude_score * 0.2)

        return {
            'quality_score': quality_score,
            'r_peaks_count': len(r_peaks),
            'rr_cv': rr_cv,
            'signal_std': np.std(ecg_cleaned),
            'cleaned_signal': ecg_cleaned
        }

    except Exception as e:
        return {'quality_score': 0.0, 'r_peaks_count': 0, 'rr_cv': float('inf'), 'error': str(e)}

def select_best_ecg_channel(raw, logger=None):
    """
    自动选择最佳ECG导联

    参数:
    raw (mne.Raw): 原始数据
    logger: 日志记录器

    返回:
    tuple: (最佳通道名, 清洗后的信号, 质量报告)
    """
    best_channel = None
    best_quality = -1
    best_signal = None
    quality_report = {}

    # 检查可用的ECG通道
    available_ecg = [ch for ch in ECG_CANDIDATES if ch in raw.ch_names]

    if not available_ecg:
        # 如果指定的通道都不存在，查找所有ECG通道
        available_ecg = [ch for ch in raw.ch_names if ch.startswith('ECG')]

    if not available_ecg:
        raise ValueError("未找到任何ECG通道")

    if logger:
        logger.info(f"评估ECG通道: {available_ecg}")

    # 评估每个ECG通道的质量
    for ch_name in available_ecg:
        try:
            ecg_data = raw.get_data(picks=[ch_name])[0]
            quality_info = evaluate_ecg_quality(ecg_data, raw.info['sfreq'])

            quality_report[ch_name] = quality_info

            if quality_info['quality_score'] > best_quality:
                best_quality = quality_info['quality_score']
                best_channel = ch_name
                best_signal = quality_info.get('cleaned_signal', ecg_data)

            if logger:
                logger.info(f"{ch_name}: 质量分数={quality_info['quality_score']:.3f}, "
                          f"R峰数={quality_info['r_peaks_count']}, "
                          f"RR变异系数={quality_info['rr_cv']:.3f}")

        except Exception as e:
            if logger:
                logger.warning(f"评估{ch_name}失败: {str(e)}")
            quality_report[ch_name] = {'quality_score': 0.0, 'error': str(e)}

    if best_channel is None:
        raise ValueError("无法找到合适的ECG通道")

    if logger:
        logger.info(f"选择最佳ECG通道: {best_channel} (质量分数: {best_quality:.3f})")

    return best_channel, best_signal, quality_report

def detect_r_peaks_optimized(ecg_signal, sampling_rate, logger=None):
    """
    优化的R波检测算法，尝试多种方法确保最佳检测效果

    参数:
    ecg_signal (numpy.ndarray): 清洗后的ECG信号
    sampling_rate (int): 采样率
    logger: 日志记录器

    返回:
    tuple: (R峰位置数组, 检测方法, 质量信息)
    """
    best_r_peaks = np.array([])
    best_method = None
    best_quality = 0

    for method in R_PEAK_METHODS:
        try:
            # 使用不同的R峰检测方法
            _, rpeaks_info = nk.ecg_peaks(ecg_signal, sampling_rate=sampling_rate, method=method)
            r_peaks = rpeaks_info.get('ECG_R_Peaks', [])

            if len(r_peaks) < QUALITY_CONTROL['min_r_peaks']:
                continue

            # 评估检测质量
            rr_intervals = np.diff(r_peaks) / sampling_rate

            # 过滤异常的RR间期
            valid_mask = (rr_intervals >= QUALITY_CONTROL['min_rr_interval']) & \
                        (rr_intervals <= QUALITY_CONTROL['max_rr_interval'])

            if np.sum(valid_mask) < len(rr_intervals) * 0.7:  # 至少70%的RR间期有效
                continue

            # 计算质量分数
            rr_cv = np.std(rr_intervals) / np.mean(rr_intervals)
            quality_score = len(r_peaks) * (1.0 - min(rr_cv, 1.0))

            if quality_score > best_quality:
                best_quality = quality_score
                best_r_peaks = r_peaks
                best_method = method

            if logger:
                logger.debug(f"方法 {method}: {len(r_peaks)} 个R峰, 质量分数: {quality_score:.2f}")

        except Exception as e:
            if logger:
                logger.debug(f"方法 {method} 失败: {str(e)}")
            continue

    if len(best_r_peaks) == 0:
        raise ValueError("所有R峰检测方法都失败")

    # 进一步优化R峰位置（精确对齐）
    optimized_r_peaks = refine_r_peak_positions(ecg_signal, best_r_peaks, sampling_rate)

    quality_info = {
        'method': best_method,
        'original_count': len(best_r_peaks),
        'final_count': len(optimized_r_peaks),
        'quality_score': best_quality
    }

    if logger:
        logger.info(f"R峰检测完成: 方法={best_method}, 检测到{len(optimized_r_peaks)}个R峰")

    return optimized_r_peaks, best_method, quality_info

def refine_r_peak_positions(ecg_signal, r_peaks, sampling_rate, window_ms=50):
    """
    精确调整R峰位置，确保对齐到真正的峰值点

    参数:
    ecg_signal (numpy.ndarray): ECG信号
    r_peaks (numpy.ndarray): 初始R峰位置
    sampling_rate (int): 采样率
    window_ms (int): 搜索窗口（毫秒）

    返回:
    numpy.ndarray: 优化后的R峰位置
    """
    window_samples = int(window_ms * sampling_rate / 1000)
    refined_peaks = []

    for peak in r_peaks:
        # 在峰值周围搜索真正的最大值
        start = max(0, peak - window_samples // 2)
        end = min(len(ecg_signal), peak + window_samples // 2)

        if start < end:
            local_signal = ecg_signal[start:end]
            local_max_idx = np.argmax(local_signal)
            refined_peak = start + local_max_idx
            refined_peaks.append(refined_peak)
        else:
            refined_peaks.append(peak)

    return np.array(refined_peaks)

def apply_enhanced_preprocessing(raw, subject_id=None, logger=None):
    """
    应用增强预处理：基于被试的自适应伪迹去除 + ICA + 滤波
    """
    try:
        raw_enhanced = raw.copy()

        # 1. 基于被试ID的自适应伪迹去除
        raw_enhanced = remove_artifacts_adaptive_by_subject(raw_enhanced, subject_id, logger)

        # 2. 应用滤波
        raw_enhanced.filter(l_freq=FILTER_LOW, h_freq=FILTER_HIGH,
                           fir_design='firwin', verbose=False)

        # 3. ICA去除系统性伪迹（眼电、肌电）
        raw_enhanced = apply_ica_artifact_removal(raw_enhanced, logger)

        if logger:
            logger.info(f"应用增强预处理: {FILTER_LOW}-{FILTER_HIGH}Hz + ICA")
        return raw_enhanced

    except Exception as e:
        if logger:
            logger.error(f"增强预处理失败: {str(e)}")
        # 如果增强预处理失败，回退到基础滤波
        return apply_basic_filtering(raw, logger)

def remove_artifacts_adaptive_by_subject(raw, subject_id=None, logger=None):
    """
    基于被试ID的自适应伪迹去除策略
    """
    try:
        # 定义需要严格处理的被试（已知问题被试）
        strict_subjects = [30]  # 30号被试需要严格处理

        # 定义需要温和处理的被试（信号质量较好）
        gentle_subjects = [1, 2, 5, 6, 7, 8, 9, 10]  # 这些被试通常信号质量较好

        raw_clean = raw.copy()
        data = raw_clean.get_data()
        data_uv = data * 1e6

        # 根据被试ID选择处理策略
        if subject_id in strict_subjects:
            # 严格处理（高强度）
            artifact_threshold = QUALITY_CONTROL['artifact_threshold']
            extreme_threshold = 50.0
            statistical_threshold = 3.5
            processing_mode = f"严格处理(被试{subject_id})"
            apply_statistical = True
        elif subject_id in gentle_subjects:
            # 温和处理（保护正常信号）
            artifact_threshold = QUALITY_CONTROL['artifact_threshold'] * 2.5
            extreme_threshold = 150.0
            statistical_threshold = 5.0
            processing_mode = f"温和处理(被试{subject_id})"
            apply_statistical = False  # 不应用统计异常值检测
        else:
            # 标准处理（中等强度）
            artifact_threshold = QUALITY_CONTROL['artifact_threshold'] * 1.5
            extreme_threshold = 100.0
            statistical_threshold = 4.0
            processing_mode = f"标准处理(被试{subject_id})"
            apply_statistical = False

        # 第一层：基础阈值伪迹去除
        artifact_mask = np.abs(data_uv) > artifact_threshold

        # 第二层：极端伪迹去除
        extreme_mask = np.abs(data_uv) > extreme_threshold

        # 第三层：统计异常值检测（仅对严格处理的被试）
        statistical_mask = np.zeros_like(data_uv, dtype=bool)
        if apply_statistical:
            for ch_idx in range(data.shape[0]):
                ch_data = data_uv[ch_idx, :]
                ch_median = np.median(ch_data)
                ch_mad = np.median(np.abs(ch_data - ch_median))

                if ch_mad > 0:
                    modified_z_scores = 0.6745 * (ch_data - ch_median) / ch_mad
                    outlier_mask = np.abs(modified_z_scores) > statistical_threshold
                    statistical_mask[ch_idx, :] = outlier_mask

        # 合并伪迹掩码
        combined_mask = artifact_mask | extreme_mask | statistical_mask

        # 统计伪迹
        total_samples = data_uv.size
        artifact_samples = np.sum(combined_mask)
        artifact_percentage = artifact_samples / total_samples * 100

        if artifact_samples > 0:
            # 使用精细插值方法替换伪迹段
            for ch_idx in range(data.shape[0]):
                ch_artifact_mask = combined_mask[ch_idx, :]
                if np.any(ch_artifact_mask):
                    clean_indices = ~ch_artifact_mask
                    clean_ratio = np.sum(clean_indices) / len(ch_artifact_mask)

                    if clean_ratio > 0.3:  # 至少30%数据干净
                        from scipy.interpolate import interp1d
                        clean_times = np.where(clean_indices)[0]
                        clean_values = data[ch_idx, clean_indices]

                        if len(clean_times) > 3:
                            interp_func = interp1d(clean_times, clean_values,
                                                 kind='cubic', fill_value='extrapolate')
                            artifact_times = np.where(ch_artifact_mask)[0]
                            data[ch_idx, artifact_times] = interp_func(artifact_times)
                        else:
                            # 使用中位数填充
                            data[ch_idx, ch_artifact_mask] = np.median(data[ch_idx, clean_indices])

        # 更新数据
        raw_clean._data = data

        if logger:
            logger.info(f"基于被试的自适应伪迹去除 ({processing_mode}): "
                       f"主阈值={artifact_threshold:.1f}μV, 极端阈值={extreme_threshold:.1f}μV, "
                       f"处理了{artifact_percentage:.2f}%的数据点")

        return raw_clean

    except Exception as e:
        if logger:
            logger.error(f"基于被试的自适应伪迹去除失败: {str(e)}")
        return raw

def apply_basic_filtering(raw, logger=None):
    """
    基础滤波（作为增强预处理的备选方案）
    """
    try:
        raw_filtered = raw.copy()
        raw_filtered.filter(l_freq=FILTER_LOW, h_freq=FILTER_HIGH,
                           fir_design='firwin', verbose=False)

        if logger:
            logger.info(f"应用基础滤波: {FILTER_LOW}-{FILTER_HIGH}Hz")
        return raw_filtered

    except Exception as e:
        if logger:
            logger.error(f"基础滤波失败: {str(e)}")
        return raw

def assess_data_quality_level(raw, logger=None):
    """
    评估数据质量等级，决定伪迹去除强度
    """
    try:
        data = raw.get_data()
        data_uv = data * 1e6

        # 计算整体信号质量指标
        overall_std = np.std(data_uv)
        overall_max = np.max(np.abs(data_uv))

        # 计算伪迹比例
        artifact_threshold = QUALITY_CONTROL['artifact_threshold']
        artifact_ratio = np.sum(np.abs(data_uv) > artifact_threshold) / data_uv.size

        # 质量等级判断（调整阈值，更精确分类）
        if artifact_ratio > 0.25 or overall_max > 10000:  # 极高伪迹
            quality_level = 'poor'
        elif artifact_ratio > 0.15 or overall_max > 2000:  # 高伪迹
            quality_level = 'moderate'
        else:  # 中低伪迹
            quality_level = 'good'

        if logger:
            logger.info(f"数据质量评估: {quality_level} (伪迹比例: {artifact_ratio:.3f}, 最大值: {overall_max:.1f}μV)")

        return quality_level, {
            'artifact_ratio': artifact_ratio,
            'overall_max': overall_max,
            'overall_std': overall_std
        }

    except Exception as e:
        if logger:
            logger.error(f"数据质量评估失败: {str(e)}")
        return 'moderate', {}

def remove_artifacts_strict(raw, logger=None):
    """
    自适应伪迹检测和去除（根据数据质量调整处理强度）
    """
    try:
        # 评估数据质量
        quality_level, quality_metrics = assess_data_quality_level(raw, logger)

        raw_clean = raw.copy()
        data = raw_clean.get_data()
        data_uv = data * 1e6

        # 根据质量等级调整处理策略
        if quality_level == 'poor':
            # 高强度处理（原超严格方案）
            artifact_threshold = QUALITY_CONTROL['artifact_threshold']
            extreme_threshold = 50.0
            statistical_threshold = 3.5
            processing_mode = "高强度"
        elif quality_level == 'moderate':
            # 中等强度处理
            artifact_threshold = QUALITY_CONTROL['artifact_threshold'] * 1.2
            extreme_threshold = 80.0
            statistical_threshold = 4.0
            processing_mode = "中等强度"
        else:  # good quality
            # 温和处理（仅基础伪迹去除，保护正常信号）
            artifact_threshold = QUALITY_CONTROL['artifact_threshold'] * 3
            extreme_threshold = 200.0  # 很高的阈值，基本不触发
            statistical_threshold = 5.0  # 很宽松的统计阈值
            processing_mode = "温和处理"

        # 第一层：基础阈值伪迹去除
        artifact_mask = np.abs(data_uv) > artifact_threshold

        # 第二层：极端伪迹去除（仅对poor和moderate质量）
        extreme_mask = np.zeros_like(data_uv, dtype=bool)
        if quality_level in ['poor', 'moderate']:
            extreme_mask = np.abs(data_uv) > extreme_threshold

        # 第三层：统计异常值检测（仅对poor质量）
        statistical_mask = np.zeros_like(data_uv, dtype=bool)
        if quality_level == 'poor':
            for ch_idx in range(data.shape[0]):
                ch_data = data_uv[ch_idx, :]
                ch_median = np.median(ch_data)
                ch_mad = np.median(np.abs(ch_data - ch_median))

                if ch_mad > 0:
                    modified_z_scores = 0.6745 * (ch_data - ch_median) / ch_mad
                    outlier_mask = np.abs(modified_z_scores) > statistical_threshold
                    statistical_mask[ch_idx, :] = outlier_mask

        # 合并伪迹掩码
        combined_mask = artifact_mask | extreme_mask | statistical_mask

        # 统计伪迹
        total_samples = data_uv.size
        artifact_samples = np.sum(combined_mask)
        artifact_percentage = artifact_samples / total_samples * 100

        if artifact_samples > 0:
            # 使用精细插值方法替换伪迹段
            for ch_idx in range(data.shape[0]):
                ch_artifact_mask = combined_mask[ch_idx, :]
                if np.any(ch_artifact_mask):
                    clean_indices = ~ch_artifact_mask
                    clean_ratio = np.sum(clean_indices) / len(ch_artifact_mask)

                    if clean_ratio > 0.3:  # 至少30%数据干净
                        from scipy.interpolate import interp1d
                        clean_times = np.where(clean_indices)[0]
                        clean_values = data[ch_idx, clean_indices]

                        if len(clean_times) > 3:
                            interp_func = interp1d(clean_times, clean_values,
                                                 kind='cubic', fill_value='extrapolate')
                            artifact_times = np.where(ch_artifact_mask)[0]
                            data[ch_idx, artifact_times] = interp_func(artifact_times)
                        else:
                            # 使用中位数填充
                            data[ch_idx, ch_artifact_mask] = np.median(data[ch_idx, clean_indices])

        # 更新数据
        raw_clean._data = data

        if logger:
            logger.info(f"自适应伪迹去除 ({processing_mode}): 主阈值={artifact_threshold:.1f}μV, "
                       f"极端阈值={extreme_threshold:.1f}μV, 处理了{artifact_percentage:.2f}%的数据点")

        return raw_clean

    except Exception as e:
        if logger:
            logger.error(f"自适应伪迹去除失败: {str(e)}")
        return raw

def apply_ica_artifact_removal(raw, logger=None):
    """
    使用ICA去除系统性伪迹（眼电、肌电）
    """
    try:
        from mne.preprocessing import ICA

        raw_ica = raw.copy()

        # 只对EEG通道应用ICA
        eeg_picks = mne.pick_types(raw_ica.info, eeg=True, ecg=False)

        if len(eeg_picks) < 10:
            if logger:
                logger.warning("EEG通道数量不足，跳过ICA处理")
            return raw_ica

        # 设置ICA参数
        n_components = min(20, len(eeg_picks) - 1)
        ica = ICA(n_components=n_components, random_state=42, max_iter=500)

        # 拟合ICA
        ica.fit(raw_ica, picks=eeg_picks)

        # 高敏感性眼电检测（80%概率去除）
        eog_indices = []
        try:
            # 检查是否有Fp1或Fp2电极
            fp_channels = [ch for ch in raw_ica.ch_names if ch in ['Fp1', 'Fp2', 'FP1', 'FP2']]

            if fp_channels:
                # 使用Fp1或Fp2作为眼电参考，降低阈值提高敏感性
                eog_ref = fp_channels[0]  # 优先使用第一个找到的Fp电极
                eog_indices, _ = ica.find_bads_eog(raw_ica, ch_name=eog_ref, threshold=1.0)  # 降低阈值
                if logger:
                    logger.info(f"使用{eog_ref}作为眼电参考检测到{len(eog_indices)}个眼电成分（高敏感性）")
            else:
                # 如果没有Fp电极，尝试标准EOG检测，降低阈值
                eog_indices, _ = ica.find_bads_eog(raw_ica, threshold=1.0)  # 降低阈值
                if logger:
                    logger.info(f"使用标准EOG检测到{len(eog_indices)}个眼电成分（高敏感性）")

        except Exception as e:
            if logger:
                logger.warning(f"EOG自动检测失败: {str(e)}, 使用基于Fp电极的高敏感性检测")
            # 使用基于Fp电极的方法检测眼电成分
            eog_indices = detect_eog_components_by_fp_electrodes(ica, raw_ica, logger)

        # 高敏感性肌电成分检测（80%概率去除）
        muscle_indices = []
        try:
            # 使用高敏感性阈值检测肌电成分
            muscle_indices, _ = ica.find_bads_muscle(raw_ica, threshold=0.8)  # 大幅降低阈值
            if logger:
                logger.info(f"标准肌电检测到{len(muscle_indices)}个肌电成分（高敏感性）")
        except Exception as e:
            if logger:
                logger.warning(f"肌电自动检测失败: {str(e)}, 使用增强频率检测")

        # 补充基于频率的肌电检测（更严格）
        additional_muscle = detect_muscle_components_enhanced(ica, raw_ica, logger)

        # 合并检测结果，去重
        all_muscle_indices = list(set(muscle_indices + additional_muscle))
        muscle_indices = all_muscle_indices

        # 高敏感性模式：总是启用额外伪迹检测
        total_artifacts = len(eog_indices) + len(muscle_indices)
        if total_artifacts < 5:  # 提高阈值，更积极地检测伪迹
            # 使用更严格的标准重新检测
            additional_eog = detect_additional_artifacts_strict(ica, raw_ica, logger)
            eog_indices.extend(additional_eog)
            if logger and additional_eog:
                logger.info(f"高敏感性模式额外检测到{len(additional_eog)}个伪迹成分")

        # 对于信噪比过高的数据，进一步加强检测
        if total_artifacts < 8:  # 如果总伪迹成分少于8个，继续检测
            ultra_strict_artifacts = detect_ultra_strict_artifacts(ica, raw_ica, logger)
            eog_indices.extend(ultra_strict_artifacts)
            if logger and ultra_strict_artifacts:
                logger.info(f"超严格模式额外检测到{len(ultra_strict_artifacts)}个伪迹成分")

        # 合并需要去除的成分
        bad_components = list(set(eog_indices + muscle_indices))

        if bad_components:
            ica.exclude = bad_components
            raw_ica = ica.apply(raw_ica)

            if logger:
                logger.info(f"ICA去除伪迹成分: 眼电{len(eog_indices)}个, 肌电{len(muscle_indices)}个")
        else:
            if logger:
                logger.info("ICA未检测到明显的伪迹成分")

        return raw_ica

    except Exception as e:
        if logger:
            logger.error(f"ICA伪迹去除失败: {str(e)}")
        return raw



def detect_eog_components_by_fp_electrodes(ica, raw, logger=None):
    """
    基于Fp1/Fp2电极检测眼电成分的专门方法
    """
    try:
        # 检查是否有Fp电极
        fp_channels = [ch for ch in raw.ch_names if ch in ['Fp1', 'Fp2', 'FP1', 'FP2']]

        if not fp_channels:
            if logger:
                logger.warning("未找到Fp1/Fp2电极，使用基于方差的检测")
            return detect_eog_components_by_variance(ica, raw, logger)

        # 获取ICA成分
        sources = ica.get_sources(raw)
        sources_data = sources.get_data()

        # 获取Fp电极数据
        fp_channel = fp_channels[0]
        fp_idx = raw.ch_names.index(fp_channel)
        fp_data = raw.get_data()[fp_idx, :]

        # 计算每个ICA成分与Fp电极的相关性
        eog_indices = []
        for comp_idx in range(sources_data.shape[0]):
            comp_data = sources_data[comp_idx, :]

            # 计算相关系数
            correlation = np.corrcoef(comp_data, fp_data)[0, 1]

            # 如果相关性较高，认为是眼电成分
            if abs(correlation) > 0.3:  # 相关性阈值
                eog_indices.append(comp_idx)

        # 限制眼电成分数量
        eog_indices = eog_indices[:5]  # 最多5个

        if logger:
            logger.info(f"基于{fp_channel}电极检测到{len(eog_indices)}个眼电成分")

        return eog_indices

    except Exception as e:
        if logger:
            logger.error(f"基于Fp电极的眼电检测失败: {str(e)}")
        return detect_eog_components_by_variance(ica, raw, logger)

def detect_eog_components_by_variance(ica, raw, logger=None):
    """
    基于方差检测眼电成分（当没有EOG通道时的备选方法）
    """
    try:
        # 获取ICA成分的时间序列
        sources = ica.get_sources(raw)
        sources_data = sources.get_data()

        # 计算每个成分的方差
        variances = np.var(sources_data, axis=1)

        # 选择方差最大的前几个成分作为可能的眼电成分
        # 眼电通常具有较大的方差
        n_components_to_check = min(3, len(variances))
        eog_candidates = np.argsort(variances)[-n_components_to_check:]

        # 进一步筛选：检查成分的频率特征
        eog_indices = []
        for comp_idx in eog_candidates:
            comp_data = sources_data[comp_idx, :]
            # 计算低频功率比例（眼电主要在低频）
            from scipy import signal as scipy_signal
            freqs, psd = scipy_signal.welch(comp_data, fs=raw.info['sfreq'], nperseg=1024)
            low_freq_mask = freqs <= 4.0  # 0-4Hz
            low_freq_power = np.sum(psd[low_freq_mask])
            total_power = np.sum(psd)
            low_freq_ratio = low_freq_power / total_power

            # 如果低频功率比例较高，认为是眼电成分
            if low_freq_ratio > 0.6:
                eog_indices.append(comp_idx)

        if logger and eog_indices:
            logger.info(f"基于方差检测到{len(eog_indices)}个疑似眼电成分")

        return eog_indices

    except Exception as e:
        if logger:
            logger.error(f"基于方差的眼电检测失败: {str(e)}")
        return []

def detect_muscle_components_enhanced(ica, raw, logger=None):
    """
    增强的肌电成分检测（更严格的标准）
    """
    try:
        # 获取ICA成分的时间序列
        sources = ica.get_sources(raw)
        sources_data = sources.get_data()

        muscle_indices = []
        for comp_idx in range(sources_data.shape[0]):
            comp_data = sources_data[comp_idx, :]

            # 计算多个频段的功率比例
            from scipy import signal as scipy_signal
            freqs, psd = scipy_signal.welch(comp_data, fs=raw.info['sfreq'], nperseg=1024)

            # 高频功率比例（肌电主要在高频）
            high_freq_mask = freqs >= 20.0  # 20Hz以上
            high_freq_power = np.sum(psd[high_freq_mask])

            # 极高频功率比例（肌电特征）
            very_high_freq_mask = freqs >= 30.0  # 30Hz以上
            very_high_freq_power = np.sum(psd[very_high_freq_mask])

            total_power = np.sum(psd)

            if total_power > 0:
                high_freq_ratio = high_freq_power / total_power
                very_high_freq_ratio = very_high_freq_power / total_power

                # 更严格的肌电检测标准
                is_muscle = (
                    high_freq_ratio > 0.25 or  # 降低阈值，提高敏感性
                    very_high_freq_ratio > 0.15 or  # 极高频检测
                    (high_freq_ratio > 0.2 and np.var(comp_data) > np.median([np.var(sources_data[i, :]) for i in range(sources_data.shape[0])]) * 2)  # 高方差 + 高频
                )

                if is_muscle:
                    muscle_indices.append(comp_idx)

        # 限制肌电成分数量，但允许更多去除
        muscle_indices = muscle_indices[:8]  # 最多8个（比之前更多）

        if logger and muscle_indices:
            logger.info(f"增强检测到{len(muscle_indices)}个肌电成分")

        return muscle_indices

    except Exception as e:
        if logger:
            logger.error(f"增强肌电检测失败: {str(e)}")
        return detect_muscle_components_by_frequency(ica, raw, logger)

def detect_muscle_components_by_frequency(ica, raw, logger=None):
    """
    基于频率特征检测肌电成分（备选方法）
    """
    try:
        # 获取ICA成分的时间序列
        sources = ica.get_sources(raw)
        sources_data = sources.get_data()

        muscle_indices = []
        for comp_idx in range(sources_data.shape[0]):
            comp_data = sources_data[comp_idx, :]

            # 计算高频功率比例（肌电主要在高频）
            from scipy import signal as scipy_signal
            freqs, psd = scipy_signal.welch(comp_data, fs=raw.info['sfreq'], nperseg=1024)
            high_freq_mask = freqs >= 20.0  # 20Hz以上
            high_freq_power = np.sum(psd[high_freq_mask])
            total_power = np.sum(psd)
            high_freq_ratio = high_freq_power / total_power

            # 如果高频功率比例较高，认为是肌电成分
            if high_freq_ratio > 0.3:
                muscle_indices.append(comp_idx)

        # 限制肌电成分数量，避免过度去除
        muscle_indices = muscle_indices[:5]  # 最多5个

        if logger and muscle_indices:
            logger.info(f"基于频率检测到{len(muscle_indices)}个疑似肌电成分")

        return muscle_indices

    except Exception as e:
        if logger:
            logger.error(f"基于频率的肌电检测失败: {str(e)}")
        return []

def detect_additional_artifacts_strict(ica, raw, logger=None):
    """
    严格模式下的额外伪迹检测（用于高信噪比数据）
    """
    try:
        # 获取ICA成分
        sources = ica.get_sources(raw)
        sources_data = sources.get_data()

        additional_artifacts = []

        for comp_idx in range(sources_data.shape[0]):
            comp_data = sources_data[comp_idx, :]

            # 计算多个伪迹特征
            from scipy import signal as scipy_signal
            freqs, psd = scipy_signal.welch(comp_data, fs=raw.info['sfreq'], nperseg=1024)

            # 1. 检查是否有异常高的方差（可能是伪迹）
            comp_var = np.var(comp_data)
            median_var = np.median([np.var(sources_data[i, :]) for i in range(sources_data.shape[0])])

            # 2. 检查频率分布异常
            low_freq_power = np.sum(psd[freqs <= 2.0])  # 0-2Hz
            high_freq_power = np.sum(psd[freqs >= 25.0])  # 25Hz以上
            total_power = np.sum(psd)

            if total_power > 0:
                low_freq_ratio = low_freq_power / total_power
                high_freq_ratio = high_freq_power / total_power

                # 严格的伪迹判断标准
                is_artifact = (
                    comp_var > median_var * 3 or  # 方差异常高
                    low_freq_ratio > 0.7 or       # 低频功率过高（眼电特征）
                    high_freq_ratio > 0.2 or      # 高频功率过高（肌电特征）
                    (low_freq_ratio > 0.5 and high_freq_ratio > 0.15)  # 混合伪迹
                )

                if is_artifact:
                    additional_artifacts.append(comp_idx)

        # 限制额外检测的成分数量
        additional_artifacts = additional_artifacts[:3]  # 最多3个额外成分

        if logger and additional_artifacts:
            logger.info(f"严格模式检测到{len(additional_artifacts)}个额外伪迹成分")

        return additional_artifacts

    except Exception as e:
        if logger:
            logger.error(f"严格伪迹检测失败: {str(e)}")
        return []

def detect_ultra_strict_artifacts(ica, raw, logger=None):
    """
    超严格模式的伪迹检测（针对信噪比过高的数据）
    """
    try:
        # 获取ICA成分
        sources = ica.get_sources(raw)
        sources_data = sources.get_data()

        ultra_strict_artifacts = []

        for comp_idx in range(sources_data.shape[0]):
            comp_data = sources_data[comp_idx, :]

            # 计算多个伪迹特征
            from scipy import signal as scipy_signal
            freqs, psd = scipy_signal.welch(comp_data, fs=raw.info['sfreq'], nperseg=1024)

            # 1. 方差分析（更严格）
            comp_var = np.var(comp_data)
            all_vars = [np.var(sources_data[i, :]) for i in range(sources_data.shape[0])]
            var_percentile = np.percentile(all_vars, 70)  # 降低到70%分位数

            # 2. 频率分析（更严格）
            very_low_freq_power = np.sum(psd[freqs <= 1.0])   # 0-1Hz（眼电）
            low_freq_power = np.sum(psd[freqs <= 4.0])        # 0-4Hz（眼电）
            high_freq_power = np.sum(psd[freqs >= 20.0])      # 20Hz以上（肌电）
            very_high_freq_power = np.sum(psd[freqs >= 30.0]) # 30Hz以上（肌电）
            total_power = np.sum(psd)

            if total_power > 0:
                very_low_freq_ratio = very_low_freq_power / total_power
                low_freq_ratio = low_freq_power / total_power
                high_freq_ratio = high_freq_power / total_power
                very_high_freq_ratio = very_high_freq_power / total_power

                # 3. 峰度分析（检测尖峰伪迹）
                from scipy.stats import kurtosis
                comp_kurtosis = kurtosis(comp_data)

                # 超严格的伪迹判断标准（80%概率去除）
                is_artifact = (
                    comp_var > var_percentile or           # 方差超过70%分位数
                    very_low_freq_ratio > 0.4 or          # 极低频功率过高
                    low_freq_ratio > 0.6 or               # 低频功率过高
                    high_freq_ratio > 0.15 or             # 高频功率过高
                    very_high_freq_ratio > 0.1 or         # 极高频功率过高
                    comp_kurtosis > 5 or                  # 峰度过高（尖峰伪迹）
                    (low_freq_ratio > 0.4 and high_freq_ratio > 0.1)  # 混合伪迹
                )

                if is_artifact:
                    ultra_strict_artifacts.append(comp_idx)

        # 限制超严格检测的成分数量，避免过度去除
        ultra_strict_artifacts = ultra_strict_artifacts[:5]  # 最多5个额外成分

        if logger and ultra_strict_artifacts:
            logger.info(f"超严格模式检测到{len(ultra_strict_artifacts)}个额外伪迹成分")

        return ultra_strict_artifacts

    except Exception as e:
        if logger:
            logger.error(f"超严格伪迹检测失败: {str(e)}")
        return []

def extract_hep_epochs(raw, r_peaks, logger=None):
    """
    提取心跳诱发电位epochs
    """
    try:
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])

        # 创建epochs（使用数据提取窗口）
        epochs = mne.Epochs(raw, events, event_id=1,
                           tmin=HEP_TMIN, tmax=HEP_TMAX,
                           baseline=(BASELINE_TMIN, BASELINE_TMAX),
                           preload=True, reject=None, verbose=False)

        if logger:
            logger.info(f"创建了 {len(epochs)} 个HEP epochs")
        return epochs

    except Exception as e:
        if logger:
            logger.error(f"HEP epochs提取失败: {str(e)}")
        return None

def validate_epochs_quality_result_oriented(epochs, subject_id=None, logger=None):
    """
    结果导向的质量验证 - 根据数据质量动态调整验证标准
    目标：将最终噪声控制在相同水平
    """
    try:
        # 检查基线稳定性
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        baseline_data = epochs.get_data()[:, :EEG_CHANNEL_COUNT, baseline_mask]

        # 转换为μV进行检查
        baseline_data_uv = baseline_data * 1e6
        baseline_std = np.std(baseline_data_uv, axis=2).mean()

        # 检查HEP成分窗口（200-600ms）
        hep_mask = (epochs.times >= 0.2) & (epochs.times <= 0.6)
        hep_data = epochs.get_data()[:, :EEG_CHANNEL_COUNT, hep_mask]
        hep_data_uv = hep_data * 1e6
        hep_std = np.std(hep_data_uv, axis=2).mean()

        # 检查伪迹
        eeg_data = epochs.get_data()[:, :EEG_CHANNEL_COUNT, :]
        eeg_data_uv = eeg_data * 1e6
        max_amplitude = np.max(np.abs(eeg_data_uv))

        # 计算信噪比
        snr = hep_std / baseline_std if baseline_std > 0 else 0

        # 结果导向的动态验证标准
        quality_issues = []
        validation_passed = True

        # 根据被试ID和实际数据质量动态调整验证阈值
        if subject_id in [30]:  # 已知问题被试
            # 对问题被试使用更宽松的验证标准（因为已经严格处理过）
            artifact_threshold = 15000.0  # 15mV，非常宽松
            baseline_threshold = 50.0     # 50μV
            processing_note = "问题被试(已严格处理)"
        elif subject_id in [1, 2, 5, 6, 7, 8, 9, 10]:  # 信号质量较好的被试
            # 对好质量被试使用适中的验证标准（因为温和处理）
            artifact_threshold = 8000.0   # 8mV
            baseline_threshold = 35.0     # 35μV
            processing_note = "优质信号(温和处理)"
        else:  # 其他被试
            # 标准验证阈值
            artifact_threshold = 10000.0  # 10mV
            baseline_threshold = 40.0     # 40μV
            processing_note = "标准处理"

        # 1. 基线稳定性检查（动态阈值）
        if baseline_std > baseline_threshold:
            quality_issues.append(f"基线不稳定: {baseline_std:.3f}μV > {baseline_threshold}μV")
            validation_passed = False

        # 2. 伪迹检查（动态阈值）
        if max_amplitude > artifact_threshold:
            quality_issues.append(f"检测到伪迹: {max_amplitude:.1f}μV > {artifact_threshold}μV")
            validation_passed = False

        # 3. HEP成分变化检查（统一标准）
        hep_change_ratio = hep_std / baseline_std if baseline_std > 0 else 0
        if hep_change_ratio < QUALITY_CONTROL['hep_change_min_ratio']:
            quality_issues.append(f"HEP变化不足: {hep_change_ratio:.2f} < {QUALITY_CONTROL['hep_change_min_ratio']}")
            validation_passed = False

        # 4. 信噪比检查（统一标准）
        if snr < QUALITY_CONTROL['snr_threshold']:
            quality_issues.append(f"信噪比过低: {snr:.2f} < {QUALITY_CONTROL['snr_threshold']}")
            validation_passed = False

        # 5. R波对齐检查（动态阈值）
        r_wave_mask = (epochs.times >= -0.05) & (epochs.times <= 0.05)  # ±50ms窗口
        r_wave_data = epochs.get_data()[:, :EEG_CHANNEL_COUNT, r_wave_mask]
        r_wave_data_uv = r_wave_data * 1e6
        r_wave_amplitude = np.max(np.abs(r_wave_data_uv))

        # R波伪迹检查（动态阈值）
        r_wave_threshold = baseline_std * 8 if subject_id in [30] else baseline_std * 5
        if r_wave_amplitude > r_wave_threshold:
            quality_issues.append(f"R波伪迹过强: {r_wave_amplitude:.1f}μV")
            # 这个不算致命错误，只是警告

        # 计算最终噪声水平（结果导向的关键指标）
        final_noise_level = baseline_std
        target_noise_level = 25.0  # 目标：将所有被试的基线噪声控制在25μV以下

        noise_control_success = final_noise_level <= target_noise_level

        quality_info = {
            'baseline_std': baseline_std,
            'hep_std': hep_std,
            'max_amplitude': max_amplitude,
            'snr': snr,
            'hep_change_ratio': hep_change_ratio,
            'r_wave_amplitude': r_wave_amplitude,
            'final_noise_level': final_noise_level,
            'target_noise_level': target_noise_level,
            'noise_control_success': noise_control_success,
            'processing_note': processing_note,
            'artifact_threshold': artifact_threshold,
            'baseline_threshold': baseline_threshold,
            'issues': quality_issues,
            'passed': validation_passed
        }

        if logger:
            logger.info(f"结果导向质量检查 ({processing_note}): "
                       f"基线={baseline_std:.2f}μV, HEP={hep_std:.2f}μV, "
                       f"SNR={snr:.2f}, 变化比={hep_change_ratio:.2f}")
            logger.info(f"噪声控制: 实际={final_noise_level:.2f}μV, 目标={target_noise_level:.2f}μV, "
                       f"成功={'是' if noise_control_success else '否'}")

            if quality_issues:
                if validation_passed:
                    logger.warning(f"质量警告: {'; '.join(quality_issues)}")
                else:
                    logger.error(f"质量验证失败: {'; '.join(quality_issues)}")

        return quality_info

    except Exception as e:
        if logger:
            logger.error(f"结果导向质量验证失败: {str(e)}")
        return {'passed': False, 'error': str(e)}

def validate_r_peak_alignment(epochs, r_peaks, sampling_rate, logger=None):
    """
    验证R波对齐精度
    """
    try:
        # 检查R波是否正确对齐到0ms位置
        time_zero_idx = np.argmin(np.abs(epochs.times))
        expected_position = epochs.times[time_zero_idx]

        # 计算实际R波位置的偏差
        position_error = abs(expected_position * 1000)  # 转换为ms

        # 检查是否在容差范围内
        tolerance_ms = QUALITY_CONTROL['r_peak_alignment_tolerance'] * 1000 / sampling_rate
        alignment_passed = position_error <= tolerance_ms

        alignment_info = {
            'expected_position_ms': 0.0,
            'actual_position_ms': expected_position * 1000,
            'position_error_ms': position_error,
            'tolerance_ms': tolerance_ms,
            'alignment_passed': alignment_passed
        }

        if logger:
            if alignment_passed:
                logger.info(f"R波对齐验证通过: 误差={position_error:.2f}ms")
            else:
                logger.error(f"R波对齐验证失败: 误差={position_error:.2f}ms > {tolerance_ms:.2f}ms")

        return alignment_info

    except Exception as e:
        if logger:
            logger.error(f"R波对齐验证失败: {str(e)}")
        return {'alignment_passed': False, 'error': str(e)}

def apply_special_processing_for_problematic_subjects(subject_id, raw, logger=None):
    """
    针对问题被试的特殊处理策略
    """
    if subject_id not in PROBLEMATIC_SUBJECTS:
        return raw, None

    try:
        special_config = PROBLEMATIC_SUBJECTS[subject_id]
        logger.info(f"应用被试{subject_id}特殊处理: {special_config['description']}")

        raw_special = raw.copy()

        # 1. 更严格的伪迹去除
        artifact_threshold = special_config.get('artifact_threshold', QUALITY_CONTROL['artifact_threshold'])
        raw_special = remove_artifacts_strict_custom(raw_special, artifact_threshold, logger)

        # 2. 统一滤波参数（所有被试使用相同参数）
        if subject_id == 30:
            # 30号被试也使用统一的滤波参数
            raw_special.filter(l_freq=FILTER_LOW, h_freq=FILTER_HIGH, fir_design='firwin', verbose=False)
            logger.info(f"被试{subject_id}使用统一滤波: {FILTER_LOW}-{FILTER_HIGH}Hz")

        return raw_special, special_config

    except Exception as e:
        if logger:
            logger.error(f"被试{subject_id}特殊处理失败: {str(e)}")
        return raw, None

def remove_artifacts_strict_custom(raw, threshold, logger=None):
    """
    自定义阈值的严格伪迹去除
    """
    try:
        raw_clean = raw.copy()
        data = raw_clean.get_data()

        # 转换为μV进行检查
        data_uv = data * 1e6

        # 检测超过阈值的伪迹
        artifact_mask = np.abs(data_uv) > threshold

        # 统计伪迹
        total_samples = data_uv.size
        artifact_samples = np.sum(artifact_mask)
        artifact_percentage = artifact_samples / total_samples * 100

        if artifact_samples > 0:
            # 使用精细插值方法替换伪迹段
            for ch_idx in range(data.shape[0]):
                ch_artifact_mask = artifact_mask[ch_idx, :]
                if np.any(ch_artifact_mask):
                    # 找到干净的数据点进行插值
                    clean_indices = ~ch_artifact_mask
                    if np.sum(clean_indices) > len(ch_artifact_mask) * 0.3:  # 至少30%数据干净
                        # 使用三次样条插值
                        from scipy.interpolate import interp1d
                        clean_times = np.where(clean_indices)[0]
                        clean_values = data[ch_idx, clean_indices]

                        if len(clean_times) > 3:  # 需要至少4个点进行三次插值
                            interp_func = interp1d(clean_times, clean_values,
                                                 kind='cubic', fill_value='extrapolate')
                            artifact_times = np.where(ch_artifact_mask)[0]
                            data[ch_idx, artifact_times] = interp_func(artifact_times)

        # 更新数据
        raw_clean._data = data

        if logger:
            logger.info(f"自定义伪迹去除: 阈值={threshold}μV, "
                       f"处理了{artifact_percentage:.2f}%的数据点")

        return raw_clean

    except Exception as e:
        if logger:
            logger.error(f"自定义伪迹去除失败: {str(e)}")
        return raw

def detect_r_peaks_for_problematic_subjects(ecg_signal, sampling_rate, subject_id, logger=None):
    """
    针对问题被试的特殊R波检测
    """
    if subject_id not in PROBLEMATIC_SUBJECTS:
        return detect_r_peaks_optimized(ecg_signal, sampling_rate, logger)

    try:
        special_config = PROBLEMATIC_SUBJECTS[subject_id]
        methods = special_config.get('r_peak_methods', R_PEAK_METHODS)

        best_r_peaks = np.array([])
        best_method = None
        best_quality = 0

        logger.info(f"被试{subject_id}使用特殊R波检测算法: {methods}")

        for method in methods:
            try:
                # 使用不同的R峰检测方法
                _, rpeaks_info = nk.ecg_peaks(ecg_signal, sampling_rate=sampling_rate, method=method)
                r_peaks = rpeaks_info.get('ECG_R_Peaks', [])

                if len(r_peaks) < QUALITY_CONTROL['min_r_peaks']:
                    continue

                # 评估检测质量
                rr_intervals = np.diff(r_peaks) / sampling_rate

                # 过滤异常的RR间期
                valid_mask = (rr_intervals >= QUALITY_CONTROL['min_rr_interval']) & \
                            (rr_intervals <= QUALITY_CONTROL['max_rr_interval'])

                if np.sum(valid_mask) < len(rr_intervals) * 0.7:  # 至少70%的RR间期有效
                    continue

                # 计算质量分数
                rr_cv = np.std(rr_intervals) / np.mean(rr_intervals)
                quality_score = len(r_peaks) * (1.0 - min(rr_cv, 1.0))

                if quality_score > best_quality:
                    best_quality = quality_score
                    best_r_peaks = r_peaks
                    best_method = method

                logger.debug(f"被试{subject_id}方法{method}: {len(r_peaks)}个R峰, 质量分数: {quality_score:.2f}")

            except Exception as e:
                logger.debug(f"被试{subject_id}方法{method}失败: {str(e)}")
                continue

        if len(best_r_peaks) == 0:
            raise ValueError(f"被试{subject_id}所有特殊R峰检测方法都失败")

        # 针对30号被试的特殊R波校正
        if subject_id == 30 and special_config.get('manual_r_peak_correction', False):
            best_r_peaks = correct_r_peaks_for_subject_30(ecg_signal, best_r_peaks, sampling_rate, logger)

        # 进一步优化R峰位置
        optimized_r_peaks = refine_r_peak_positions(ecg_signal, best_r_peaks, sampling_rate)

        quality_info = {
            'method': best_method,
            'original_count': len(best_r_peaks),
            'final_count': len(optimized_r_peaks),
            'quality_score': best_quality,
            'special_processing': True,
            'subject_id': subject_id
        }

        logger.info(f"被试{subject_id}特殊R峰检测完成: 方法={best_method}, 检测到{len(optimized_r_peaks)}个R峰")

        return optimized_r_peaks, best_method, quality_info

    except Exception as e:
        logger.error(f"被试{subject_id}特殊R峰检测失败: {str(e)}")
        # 回退到标准方法
        return detect_r_peaks_optimized(ecg_signal, sampling_rate, logger)

def correct_r_peaks_for_subject_30(ecg_signal, r_peaks, sampling_rate, logger=None):
    """
    针对30号被试的R波位置校正（解决20个采样点偏差问题）
    """
    try:
        corrected_peaks = []
        correction_offset = 20  # 已知的偏差量

        for peak in r_peaks:
            # 在峰值周围搜索真正的最大值
            search_start = max(0, peak - correction_offset - 10)
            search_end = min(len(ecg_signal), peak + correction_offset + 10)

            if search_start < search_end:
                local_signal = ecg_signal[search_start:search_end]
                local_max_idx = np.argmax(local_signal)
                corrected_peak = search_start + local_max_idx
                corrected_peaks.append(corrected_peak)
            else:
                corrected_peaks.append(peak)

        corrected_peaks = np.array(corrected_peaks)

        if logger:
            avg_correction = np.mean(corrected_peaks - r_peaks)
            logger.info(f"被试30 R波校正: 平均校正量={avg_correction:.1f}个采样点")

        return corrected_peaks

    except Exception as e:
        if logger:
            logger.error(f"被试30 R波校正失败: {str(e)}")
        return r_peaks

def organize_electrode_data(epochs_data, ch_names, logger=None):
    """
    按照电极组合组织数据
    """
    try:
        organized_data = {}

        # 全通道数据（前61个脑电 + 后58个心电）
        organized_data['all_channels_hep'] = epochs_data

        # 中央电极数据
        central_indices = []
        for electrode in CENTRAL_ELECTRODES:
            if electrode in ch_names:
                central_indices.append(ch_names.index(electrode))

        if central_indices:
            organized_data['central_electrodes_hep'] = epochs_data[:, central_indices, :]

        # 右半球电极数据
        right_indices = []
        for electrode in RIGHT_HEMISPHERE:
            if electrode in ch_names:
                right_indices.append(ch_names.index(electrode))

        if right_indices:
            organized_data['right_hemisphere_hep'] = epochs_data[:, right_indices, :]

        # 左半球电极数据
        left_indices = []
        for electrode in LEFT_HEMISPHERE:
            if electrode in ch_names:
                left_indices.append(ch_names.index(electrode))

        if left_indices:
            organized_data['left_hemisphere_hep'] = epochs_data[:, left_indices, :]

        return organized_data

    except Exception as e:
        if logger:
            logger.error(f"数据组织失败: {str(e)}")
        return {}

def create_hep_visualization_with_quality_assessment(epochs, subject_id, stage_type, stage_number,
                                                   ch_names, ecg_channel, r_peak_method,
                                                   quality_info, alignment_info, logger=None):
    """
    创建包含质量评估的HEP波形可视化图片

    参数:
    epochs (mne.Epochs): HEP epochs
    subject_id (int): 被试ID
    stage_type (str): 阶段类型
    stage_number (str): 阶段编号
    ch_names (list): 通道名称列表
    ecg_channel (str): 使用的ECG通道
    r_peak_method (str): R峰检测方法
    quality_info (dict): 质量评估信息
    alignment_info (dict): R波对齐信息
    logger: 日志记录器

    返回:
    str: 保存的图片路径
    """
    try:
        # 创建图形，确保每个子图individually的2:1宽高比
        # 精确计算图形尺寸以确保每个子图为2:1比例
        subplot_width = 6   # 每个子图宽度
        subplot_height = 3  # 每个子图高度（2:1比例）
        quality_height = 2.5  # 质量信息区域高度

        # 计算总尺寸，考虑边距和间距
        total_width = subplot_width * 3 + 2  # 3个子图 + 间距
        total_height = subplot_height + quality_height + 1  # 子图 + 质量区域 + 间距

        fig = plt.figure(figsize=(total_width, total_height))

        # 创建精确的网格布局，确保子图区域为2:1比例
        gs = fig.add_gridspec(2, 3,
                             height_ratios=[subplot_height, quality_height],
                             hspace=0.3, wspace=0.2,
                             left=0.06, right=0.96, top=0.92, bottom=0.10)

        # 波形图区域 - 每个子图精确设置2:1宽高比
        axes = []
        for i in range(3):
            ax = fig.add_subplot(gs[0, i])
            axes.append(ax)

        # 质量信息区域
        quality_ax = fig.add_subplot(gs[1, :])

        # 确定验证状态和颜色
        validation_status = quality_info.get('validation_status', 'unknown')
        status_color = 'green' if validation_status == 'passed_strict_validation' else 'red'

        fig.suptitle(f'Subject {subject_id:02d} - {stage_type}_{stage_number} HEP Average - 严格质量控制\n'
                    f'ECG: {ecg_channel}, Method: {r_peak_method}, Status: {validation_status}',
                    fontsize=14, color=status_color)

        # 获取时间轴（转换为毫秒）
        times_ms = epochs.times * 1000

        # 创建可视化时间窗口的掩码
        vis_mask = (times_ms >= VIS_TMIN * 1000) & (times_ms <= VIS_TMAX * 1000)
        vis_times = times_ms[vis_mask]

        # 转换数据为μV
        epochs_data_uv = epochs.get_data() * 1e6

        # 定义电极组合和标题
        electrode_groups = [
            (CENTRAL_ELECTRODES, 'Central', 'red'),
            (LEFT_HEMISPHERE, 'Left Hemisphere', 'blue'),
            (RIGHT_HEMISPHERE, 'Right Hemisphere', 'green')
        ]

        for i, (electrodes, title, color) in enumerate(electrode_groups):
            ax = axes[i]

            # 找到可用的电极索引
            electrode_indices = []
            for electrode in electrodes:
                if electrode in ch_names:
                    electrode_indices.append(ch_names.index(electrode))

            if electrode_indices:
                # 提取该组电极的数据并计算平均
                group_data = epochs_data_uv[:, electrode_indices, :]
                # 先对电极求平均，再对epochs求平均
                electrode_avg = np.mean(group_data, axis=1)  # 对电极求平均 (epochs, time)
                avg_data = np.mean(electrode_avg, axis=0)    # 对epochs求平均 (time,)

                # 只显示可视化窗口的数据
                vis_avg_data = avg_data[vis_mask]

                # 调试信息：检查数据范围
                if logger:
                    logger.debug(f"{title} 数据范围: {np.min(vis_avg_data):.2f} 到 {np.max(vis_avg_data):.2f} μV")

                # 绘制波形
                ax.plot(vis_times, vis_avg_data, color=color, linewidth=2,
                       label=f'{title} (n={len(electrode_indices)})')

                # 添加基线和刺激时间标记
                ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
                ax.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='R-peak')
                ax.axvspan(BASELINE_TMIN*1000, BASELINE_TMAX*1000, alpha=0.2, color='gray', label='Baseline')

                # 设置坐标轴
                ax.set_xlabel('Time (ms)', fontsize=12)
                ax.set_ylabel('Amplitude (μV)', fontsize=12)
                ax.set_title(title, fontsize=14)
                ax.grid(True, alpha=0.3)
                ax.legend(fontsize=10)

                # 设置X轴范围为可视化窗口
                ax.set_xlim(VIS_TMIN * 1000, VIS_TMAX * 1000)

                # 根据实际数据范围自动调整Y轴
                y_range = np.max(vis_avg_data) - np.min(vis_avg_data)
                y_margin = y_range * 0.1
                y_min_auto = np.min(vis_avg_data) - y_margin
                y_max_auto = np.max(vis_avg_data) + y_margin
                ax.set_ylim(y_min_auto, y_max_auto)

                # 设置刻度标签大小
                ax.tick_params(axis='both', which='major', labelsize=11)

                # 强制设置每个子图的2:1宽高比
                # 不使用aspect ratio，而是通过图形尺寸控制比例
                # 因为aspect ratio会与数据范围冲突，导致显示异常

                # 确保子图显示正常的数据范围
                if logger:
                    logger.debug(f"{title} Y轴范围: {y_min_auto:.2f} 到 {y_max_auto:.2f} μV")
                    logger.debug(f"{title} 时间范围: {VIS_TMIN*1000:.0f} 到 {VIS_TMAX*1000:.0f} ms")
            else:
                ax.text(0.5, 0.5, f'No {title} electrodes', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{title} (No Data)')
                ax.set_xlim(VIS_TMIN * 1000, VIS_TMAX * 1000)

        # 添加质量信息显示
        quality_ax.axis('off')  # 隐藏坐标轴

        # 准备质量信息文本（结果导向）
        quality_text = []
        processing_note = quality_info.get('processing_note', '标准处理')
        quality_text.append(f"=== 结果导向质量控制评估 ({processing_note}) ===")

        # 核心指标：噪声控制结果
        final_noise = quality_info.get('final_noise_level', 0)
        target_noise = quality_info.get('target_noise_level', 25)
        noise_success = quality_info.get('noise_control_success', False)
        noise_status = "✓ 成功" if noise_success else "✗ 需改进"
        quality_text.append(f"噪声控制: {final_noise:.2f}μV / {target_noise:.2f}μV ({noise_status})")

        # 生理学指标
        quality_text.append(f"基线稳定性: {quality_info.get('baseline_std', 0):.2f}μV (阈值: <{quality_info.get('baseline_threshold', 30)}μV)")
        quality_text.append(f"HEP变化比例: {quality_info.get('hep_change_ratio', 0):.2f} (标准: ≥{QUALITY_CONTROL['hep_change_min_ratio']})")
        quality_text.append(f"信噪比: {quality_info.get('snr', 0):.2f} (标准: ≥{QUALITY_CONTROL['snr_threshold']})")

        # 伪迹控制
        artifact_threshold = quality_info.get('artifact_threshold', 500)
        quality_text.append(f"最大幅度: {quality_info.get('max_amplitude', 0):.1f}μV (阈值: <{artifact_threshold}μV)")
        quality_text.append(f"R波对齐误差: {alignment_info.get('position_error_ms', 0):.2f}ms (标准: <{alignment_info.get('tolerance_ms', 0):.2f}ms)")

        # 添加验证问题
        issues = quality_info.get('issues', [])
        if issues:
            quality_text.append("质量问题:")
            for issue in issues:
                quality_text.append(f"  • {issue}")
        else:
            quality_text.append("✓ 所有质量检查通过")

        # 显示质量信息（使用正确的字体）
        quality_text_str = '\n'.join(quality_text)

        # 检查是否有自定义字体
        if os.path.exists(font_path):
            font_prop = fm.FontProperties(fname=font_path)
            quality_ax.text(0.05, 0.95, quality_text_str, transform=quality_ax.transAxes,
                           fontsize=10, verticalalignment='top', fontproperties=font_prop,
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.8))
        else:
            # 使用系统字体
            quality_ax.text(0.05, 0.95, quality_text_str, transform=quality_ax.transAxes,
                           fontsize=10, verticalalignment='top', fontfamily='sans-serif',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.8))

        # 手动调整布局，避免tight_layout警告
        try:
            # 使用constrained_layout或手动调整
            fig.subplots_adjust(left=0.05, right=0.98, top=0.90, bottom=0.08,
                              hspace=0.25, wspace=0.15)
        except Exception as layout_e:
            if logger:
                logger.warning(f"布局调整警告: {str(layout_e)}")

        # 保存图片
        filename = f"{subject_id:02d}_{stage_type}_{stage_number}_hep_quality_assessment.png"
        filepath = os.path.join(PLOTS_DIR, filename)
        plt.savefig(filepath, dpi=100, bbox_inches='tight', facecolor='white')
        plt.close()

        if logger:
            logger.info(f"保存HEP质量评估图片: {filename}")

        return filepath

    except Exception as e:
        if logger:
            logger.error(f"创建HEP质量评估可视化失败: {str(e)}")
        return None

def save_hep_to_hdf5(stage_data, stage_type, stage_number, output_dir, logger=None):
    """
    将HEP数据保存为HDF5格式
    """
    try:
        filename = f"{stage_type}_{stage_number}_hep_data.h5"
        filepath = os.path.join(output_dir, filename)

        with h5py.File(filepath, 'w') as f:
            for data_type, data_list in stage_data.items():
                if data_list:
                    combined_data = np.concatenate(data_list, axis=0)
                    f.create_dataset(data_type, data=combined_data, compression='gzip')
                    if logger:
                        logger.info(f"保存 {data_type}: {combined_data.shape}")

        if logger:
            logger.info(f"成功保存HDF5文件: {filename}")
        return True

    except Exception as e:
        if logger:
            logger.error(f"保存HDF5文件失败: {str(e)}")
        return False

def save_quality_report(subject_id, stage_type, stage_number, quality_data, logger=None):
    """
    保存质量控制报告

    参数:
    subject_id (int): 被试ID
    stage_type (str): 阶段类型
    stage_number (str): 阶段编号
    quality_data (dict): 质量数据
    logger: 日志记录器
    """
    try:
        filename = f"{subject_id:02d}_{stage_type}_{stage_number}_quality_report.json"
        filepath = os.path.join(REPORTS_DIR, filename)

        import json
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(quality_data, f, indent=2, ensure_ascii=False, default=str)

        if logger:
            logger.debug(f"保存质量报告: {filename}")

    except Exception as e:
        if logger:
            logger.error(f"保存质量报告失败: {str(e)}")

def process_single_file_optimized(subject_id, stage_number, stage_type, logger):
    """
    优化的单文件处理函数

    参数:
    subject_id (int): 被试ID
    stage_number (str): 阶段编号
    stage_type (str): 阶段类型
    logger: 日志记录器

    返回:
    dict: 包含处理结果和质量信息的字典
    """
    processing_start_time = time.time()

    try:
        logger.info(f"开始处理被试 {subject_id:02d} - {stage_type}_{stage_number}")

        # 1. 加载数据
        raw = load_data(subject_id, stage_number, stage_type, logger)
        if raw is None:
            return None

        # 2. 检查是否为问题被试，应用特殊预处理
        raw_special, special_config = apply_special_processing_for_problematic_subjects(subject_id, raw, logger)

        # 3. 应用增强预处理（基于被试的自适应伪迹去除 + ICA + 滤波）
        raw_processed = apply_enhanced_preprocessing(raw_special, subject_id, logger)

        # 4. 自动选择最佳ECG导联（考虑特殊配置）
        try:
            if special_config and 'ecg_candidates' in special_config:
                # 临时修改ECG候选列表
                original_candidates = ECG_CANDIDATES.copy()
                ECG_CANDIDATES[:] = special_config['ecg_candidates']
                logger.info(f"被试{subject_id}使用特殊ECG导联顺序: {ECG_CANDIDATES}")

            best_ecg_channel, best_ecg_signal, ecg_quality_report = select_best_ecg_channel(raw_processed, logger)

            # 恢复原始候选列表
            if special_config and 'ecg_candidates' in special_config:
                ECG_CANDIDATES[:] = original_candidates

        except Exception as e:
            logger.error(f"ECG导联选择失败: {str(e)}")
            return None

        # 5. 优化的R峰检测（考虑特殊处理）
        try:
            if subject_id in PROBLEMATIC_SUBJECTS:
                r_peaks, r_peak_method, r_peak_quality = detect_r_peaks_for_problematic_subjects(
                    best_ecg_signal, raw_processed.info['sfreq'], subject_id, logger)
            else:
                r_peaks, r_peak_method, r_peak_quality = detect_r_peaks_optimized(
                    best_ecg_signal, raw_processed.info['sfreq'], logger)
        except Exception as e:
            logger.error(f"R峰检测失败: {str(e)}")
            return None

        # 5. 提取HEP epochs
        epochs = extract_hep_epochs(raw_processed, r_peaks, logger)
        if epochs is None:
            return None

        # 6. 结果导向的质量验证
        quality_info = validate_epochs_quality_result_oriented(epochs, subject_id, logger)

        # 7. 验证R波对齐精度
        alignment_info = validate_r_peak_alignment(epochs, r_peaks, raw_processed.info['sfreq'], logger)

        # 8. 检查是否通过所有验证
        if not quality_info.get('passed', False) or not alignment_info.get('alignment_passed', False):
            logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 未通过严格质量验证")
            # 根据严格标准，不通过验证的数据将被标记但仍保存（用于后续分析）
            quality_info['validation_status'] = 'failed_strict_validation'
        else:
            quality_info['validation_status'] = 'passed_strict_validation'

        # 7. 创建包含质量评估的可视化图片
        create_hep_visualization_with_quality_assessment(epochs, subject_id, stage_type, stage_number,
                                                        epochs.ch_names, best_ecg_channel, r_peak_method,
                                                        quality_info, alignment_info, logger)

        # 8. 组织电极数据
        epochs_data = epochs.get_data()
        ch_names = epochs.ch_names
        organized_data = organize_electrode_data(epochs_data, ch_names, logger)

        # 9. 编译完整质量报告
        processing_time = time.time() - processing_start_time
        complete_quality_report = {
            'subject_id': subject_id,
            'stage_type': stage_type,
            'stage_number': stage_number,
            'processing_time': processing_time,
            'special_processing': {
                'is_problematic_subject': subject_id in PROBLEMATIC_SUBJECTS,
                'special_config': special_config,
                'description': special_config.get('description', 'N/A') if special_config else 'N/A'
            },
            'ecg_channel_selection': {
                'selected_channel': best_ecg_channel,
                'quality_scores': ecg_quality_report
            },
            'r_peak_detection': r_peak_quality,
            'r_peak_alignment': alignment_info,
            'epochs_quality': quality_info,
            'final_epochs_count': len(epochs),
            'data_shapes': {k: v.shape for k, v in organized_data.items()},
            'processing_success': True,
            'quality_control_version': 'strict_physiological_standards_v1.0'
        }

        # 10. 保存质量报告
        save_quality_report(subject_id, stage_type, stage_number, complete_quality_report, logger)

        logger.info(f"成功处理被试 {subject_id:02d} - {stage_type}_{stage_number} "
                   f"({len(epochs)} epochs, {processing_time:.1f}s, ECG: {best_ecg_channel})")

        return {
            'organized_data': organized_data,
            'quality_report': complete_quality_report
        }

    except Exception as e:
        processing_time = time.time() - processing_start_time
        error_report = {
            'subject_id': subject_id,
            'stage_type': stage_type,
            'stage_number': stage_number,
            'processing_time': processing_time,
            'error': str(e),
            'processing_success': False
        }
        save_quality_report(subject_id, stage_type, stage_number, error_report, logger)
        logger.error(f"处理被试 {subject_id:02d} 失败: {str(e)}")
        return None

class ProgressMonitor:
    """进度监控类 - 优化版本"""

    def __init__(self, total_files, logger):
        self.total_files = total_files
        self.processed_files = 0
        self.failed_files = 0
        self.start_time = time.time()
        self.last_report_time = time.time()
        self.logger = logger
        self.quality_stats = {
            'ecg_channel_usage': {},
            'r_peak_methods': {},
            'processing_times': [],
            'epochs_counts': []
        }

    def update(self, success=True, quality_report=None):
        """更新进度和质量统计"""
        if success:
            self.processed_files += 1
            if quality_report:
                # 统计ECG通道使用情况
                ecg_channel = quality_report.get('ecg_channel_selection', {}).get('selected_channel')
                if ecg_channel:
                    self.quality_stats['ecg_channel_usage'][ecg_channel] = \
                        self.quality_stats['ecg_channel_usage'].get(ecg_channel, 0) + 1

                # 统计R峰检测方法
                r_peak_method = quality_report.get('r_peak_detection', {}).get('method')
                if r_peak_method:
                    self.quality_stats['r_peak_methods'][r_peak_method] = \
                        self.quality_stats['r_peak_methods'].get(r_peak_method, 0) + 1

                # 统计处理时间和epochs数量
                processing_time = quality_report.get('processing_time')
                if processing_time:
                    self.quality_stats['processing_times'].append(processing_time)

                epochs_count = quality_report.get('final_epochs_count')
                if epochs_count:
                    self.quality_stats['epochs_counts'].append(epochs_count)
        else:
            self.failed_files += 1

        # 检查是否需要报告进度
        current_time = time.time()
        if current_time - self.last_report_time >= PROGRESS_REPORT_INTERVAL:
            self.report_progress()
            self.last_report_time = current_time

    def report_progress(self):
        """报告当前进度和质量统计"""
        elapsed_time = time.time() - self.start_time
        completed = self.processed_files + self.failed_files

        if completed > 0:
            avg_time_per_file = elapsed_time / completed
            remaining_files = self.total_files - completed
            estimated_remaining_time = avg_time_per_file * remaining_files

            self.logger.info(f"=== 进度报告 ===")
            self.logger.info(f"已完成: {completed}/{self.total_files} ({completed/self.total_files*100:.1f}%)")
            self.logger.info(f"成功: {self.processed_files}, 失败: {self.failed_files}")
            self.logger.info(f"已用时间: {elapsed_time/3600:.1f}小时")
            self.logger.info(f"预计剩余时间: {estimated_remaining_time/3600:.1f}小时")
            self.logger.info(f"成功率: {self.processed_files/completed*100:.1f}%")

            # 质量统计报告
            if self.quality_stats['ecg_channel_usage']:
                self.logger.info(f"ECG通道使用统计: {self.quality_stats['ecg_channel_usage']}")

            if self.quality_stats['r_peak_methods']:
                self.logger.info(f"R峰检测方法统计: {self.quality_stats['r_peak_methods']}")

            if self.quality_stats['processing_times']:
                avg_time = np.mean(self.quality_stats['processing_times'])
                self.logger.info(f"平均处理时间: {avg_time:.1f}秒/文件")

            if self.quality_stats['epochs_counts']:
                avg_epochs = np.mean(self.quality_stats['epochs_counts'])
                self.logger.info(f"平均epochs数量: {avg_epochs:.1f}")

    def final_report(self):
        """最终报告"""
        total_time = time.time() - self.start_time
        self.logger.info(f"\n=== 最终处理报告 ===")
        self.logger.info(f"总文件数: {self.total_files}")
        self.logger.info(f"成功处理: {self.processed_files}")
        self.logger.info(f"处理失败: {self.failed_files}")
        self.logger.info(f"成功率: {self.processed_files/self.total_files*100:.1f}%")
        self.logger.info(f"总处理时间: {total_time/3600:.1f}小时")
        self.logger.info(f"平均每文件: {total_time/self.total_files:.1f}秒")

        # 详细质量统计
        self.logger.info(f"\n=== 质量控制统计 ===")
        self.logger.info(f"ECG通道使用分布: {self.quality_stats['ecg_channel_usage']}")
        self.logger.info(f"R峰检测方法分布: {self.quality_stats['r_peak_methods']}")

        if self.quality_stats['epochs_counts']:
            epochs_array = np.array(self.quality_stats['epochs_counts'])
            self.logger.info(f"Epochs统计: 平均={np.mean(epochs_array):.1f}, "
                           f"最小={np.min(epochs_array)}, 最大={np.max(epochs_array)}")

def generate_summary_report(progress_monitor, logger):
    """生成总结报告"""
    try:
        summary_file = os.path.join(REPORTS_DIR, 'processing_summary.json')

        summary_data = {
            'processing_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_files': progress_monitor.total_files,
            'successful_files': progress_monitor.processed_files,
            'failed_files': progress_monitor.failed_files,
            'success_rate': progress_monitor.processed_files / progress_monitor.total_files * 100,
            'total_processing_time_hours': (time.time() - progress_monitor.start_time) / 3600,
            'quality_statistics': progress_monitor.quality_stats,
            'parameters': {
                'data_extraction_window': f"{HEP_TMIN}s to {HEP_TMAX}s",
                'visualization_window': f"{VIS_TMIN}s to {VIS_TMAX}s",
                'baseline_correction': f"{BASELINE_TMIN}s to {BASELINE_TMAX}s",
                'filter_range': f"{FILTER_LOW}-{FILTER_HIGH}Hz",
                'ecg_candidates': ECG_CANDIDATES,
                'r_peak_methods': R_PEAK_METHODS
            }
        }

        import json
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False, default=str)

        logger.info(f"生成总结报告: {summary_file}")

    except Exception as e:
        logger.error(f"生成总结报告失败: {str(e)}")

def main(background_mode=False):
    """主函数 - 优化版本"""
    # 设置日志
    logger = setup_logging(background_mode)

    logger.info("开始HEP数据提取 - 严格质量控制优化版本")
    logger.info(f"数据源目录: {DATA_DIR}")
    logger.info(f"输出目录: {OUTPUT_DIR}")
    logger.info(f"图片目录: {PLOTS_DIR}")
    logger.info(f"报告目录: {REPORTS_DIR}")

    # 严格质量控制参数信息
    logger.info("=== 严格质量控制标准 ===")
    logger.info(f"伪迹阈值: {QUALITY_CONTROL['artifact_threshold']}μV (严格)")
    logger.info(f"基线稳定性: <{QUALITY_CONTROL['baseline_std_threshold']}μV (严格)")
    logger.info(f"HEP变化要求: ≥{QUALITY_CONTROL['hep_change_min_ratio']}倍基线 (严格)")
    logger.info(f"R波对齐精度: ±{QUALITY_CONTROL['r_peak_alignment_tolerance']}采样点 (严格)")
    logger.info(f"信噪比要求: ≥{QUALITY_CONTROL['snr_threshold']} (严格)")

    # 技术参数信息
    logger.info("=== 技术参数 ===")
    logger.info(f"数据提取窗口: {HEP_TMIN}s 到 {HEP_TMAX}s")
    logger.info(f"可视化窗口: {VIS_TMIN}s 到 {VIS_TMAX}s")
    logger.info(f"滤波参数: {FILTER_LOW}-{FILTER_HIGH}Hz (保留低频HEP成分)")
    logger.info(f"ECG导联候选: {ECG_CANDIDATES}")
    logger.info(f"R峰检测方法: {R_PEAK_METHODS}")

    # 特殊处理信息
    if PROBLEMATIC_SUBJECTS:
        logger.info("=== 特殊处理被试 ===")
        for subj_id, config in PROBLEMATIC_SUBJECTS.items():
            logger.info(f"被试{subj_id}: {config['description']}")

    logger.info("=== 质量控制原则 ===")
    logger.info("绝不放宽验证标准 - 通过改善预处理技术解决质量问题")
    logger.info("不符合严格生理学标准的数据将被标记但保留用于分析")

    # 统计信息
    total_files = len(SUBJECTS) * len(STAGE_NUMBERS) * len(STAGES)
    progress_monitor = ProgressMonitor(total_files, logger)

    logger.info(f"计划处理 {total_files} 个文件")
    logger.info(f"被试数量: {len(SUBJECTS)}")
    logger.info(f"阶段数量: {len(STAGES)} × {len(STAGE_NUMBERS)} = {len(STAGES) * len(STAGE_NUMBERS)}")

    # 按阶段处理数据
    for stage_type in STAGES:
        for stage_number in STAGE_NUMBERS:
            logger.info(f"\n开始处理阶段: {stage_type}_{stage_number}")

            # 初始化该阶段的数据存储
            stage_data = {
                'all_channels_hep': [],
                'central_electrodes_hep': [],
                'right_hemisphere_hep': [],
                'left_hemisphere_hep': []
            }

            processed_subjects = []

            # 处理所有被试
            for subject_id in SUBJECTS:
                result = process_single_file_optimized(subject_id, stage_number, stage_type, logger)

                if result:
                    # 添加到阶段数据中
                    organized_data = result['organized_data']
                    quality_report = result['quality_report']

                    for data_type, data in organized_data.items():
                        if data_type in stage_data:
                            stage_data[data_type].append(data)

                    processed_subjects.append(subject_id)
                    progress_monitor.update(success=True, quality_report=quality_report)
                else:
                    progress_monitor.update(success=False)

            # 保存该阶段的最终数据
            if any(stage_data.values()):
                save_hep_to_hdf5(stage_data, stage_type, stage_number, OUTPUT_DIR, logger)
                logger.info(f"阶段 {stage_type}_{stage_number} 完成: {len(processed_subjects)} 个被试成功处理")
            else:
                logger.warning(f"阶段 {stage_type}_{stage_number} 没有有效数据")

    # 最终报告
    progress_monitor.final_report()

    # 生成总结报告
    generate_summary_report(progress_monitor, logger)

    logger.info("HEP数据提取完成!")

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='HEP数据提取脚本 - 全面质量控制优化版本')
    parser.add_argument('--background', action='store_true', help='后台运行模式')
    args = parser.parse_args()

    main(background_mode=args.background)