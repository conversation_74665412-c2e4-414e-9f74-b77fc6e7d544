#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试双峰问题修复效果
使用标准I导联和双峰检测算法
"""

import sys
import os
import argparse
import logging
import time
import warnings
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import mne
import neurokit2 as nk

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入修复后的HEP提取模块
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__))))
import importlib.util
spec = importlib.util.spec_from_file_location("hep_extraction", "01_hep_extraction.py")
hep_extraction = importlib.util.module_from_spec(spec)
spec.loader.exec_module(hep_extraction)

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']

plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

# 数据路径配置
DATA_DIR = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-HBA\result\hep_analysis\14_rest_vs_test_analysis\hepdata"
PLOTS_DIR = os.path.join(OUTPUT_DIR, 'plots')
os.makedirs(PLOTS_DIR, exist_ok=True)

def setup_logging():
    """设置日志系统"""
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    logging.basicConfig(level=logging.INFO, format=log_format)
    return logging.getLogger(__name__)

def load_data(subject_id, stage_number, stage_type, logger):
    """加载指定被试和阶段的数据"""
    try:
        filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_{stage_type}_TP9TP10Ref.fif"
        file_path = os.path.join(DATA_DIR, filename)

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {filename}")
            return None

        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
        return raw

    except Exception as e:
        logger.error(f"加载数据失败 {filename}: {str(e)}")
        return None

def test_double_peak_detection(subject_id, logger):
    """测试双峰检测和修复"""
    try:
        logger.info(f"=== 测试被试 {subject_id:02d} 双峰问题检测 ===")
        
        # 加载数据
        raw = load_data(subject_id, '02', 'test', logger)
        if raw is None:
            return None
        
        # 测试ECG通道选择（包含双峰检测）
        try:
            best_channel, best_signal, quality_report = hep_extraction.select_best_ecg_channel(raw, logger)
            
            logger.info(f"选择的最佳ECG通道: {best_channel}")
            
            # 分析质量报告
            for ch_name, quality_info in quality_report.items():
                if 'error' not in quality_info:
                    double_peak_info = quality_info.get('double_peak_info', {})
                    has_double_peak = double_peak_info.get('has_double_peak_issue', False)
                    double_peak_ratio = double_peak_info.get('double_peak_ratio', 0)
                    
                    logger.info(f"{ch_name}: 质量分数={quality_info['quality_score']:.3f}, "
                               f"双峰问题={has_double_peak}, "
                               f"双峰比例={double_peak_ratio:.2f}")
            
            # 创建对比可视化
            create_double_peak_comparison(subject_id, raw, quality_report, best_channel, logger)
            
            result = {
                'subject_id': subject_id,
                'success': True,
                'best_channel': best_channel,
                'quality_report': quality_report
            }
            
            return result
            
        except Exception as e:
            logger.error(f"ECG通道选择失败: {str(e)}")
            return None
            
    except Exception as e:
        logger.error(f"测试被试 {subject_id:02d} 失败: {str(e)}")
        return None

def create_double_peak_comparison(subject_id, raw, quality_report, best_channel, logger):
    """创建双峰问题对比图"""
    try:
        # 找到有双峰问题的通道和最佳通道
        double_peak_channels = []
        good_channels = []
        
        for ch_name, quality_info in quality_report.items():
            if 'error' not in quality_info:
                double_peak_info = quality_info.get('double_peak_info', {})
                if double_peak_info.get('has_double_peak_issue', False):
                    double_peak_channels.append((ch_name, quality_info))
                else:
                    good_channels.append((ch_name, quality_info))
        
        # 创建对比图
        fig_height = max(6, len(quality_report) * 2)
        fig, axes = plt.subplots(len(quality_report), 1, figsize=(15, fig_height))
        
        if len(quality_report) == 1:
            axes = [axes]
        
        for i, (ch_name, quality_info) in enumerate(quality_report.items()):
            ax = axes[i]
            
            if 'error' in quality_info:
                ax.text(0.5, 0.5, f'{ch_name}: 分析失败\n{quality_info["error"]}', 
                       ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{ch_name} (分析失败)')
                continue
            
            # 获取信号数据
            if ch_name == 'Lead_I':
                # 计算标准I导联
                lead_I_signal, success = hep_extraction.calculate_standard_lead_I(raw, logger)
                if success:
                    signal_data = quality_info.get('cleaned_signal', lead_I_signal)
                else:
                    continue
            else:
                ecg_data = raw.get_data(picks=[ch_name])[0]
                signal_data = quality_info.get('cleaned_signal', ecg_data)
            
            # 显示前5秒的数据
            display_samples = min(2500, len(signal_data))  # 5秒 * 500Hz
            times = np.arange(display_samples) / 500.0
            
            # 获取R峰位置
            r_peaks = quality_info.get('r_peaks', [])
            display_r_peaks = [p for p in r_peaks if p < display_samples]
            
            # 绘制信号
            ax.plot(times, signal_data[:display_samples], 'b-', linewidth=1, alpha=0.7)
            
            # 标记R峰
            if display_r_peaks:
                ax.scatter(np.array(display_r_peaks)/500.0, 
                          signal_data[display_r_peaks], 
                          color='red', s=30, zorder=5)
            
            # 获取双峰信息
            double_peak_info = quality_info.get('double_peak_info', {})
            has_double_peak = double_peak_info.get('has_double_peak_issue', False)
            double_peak_ratio = double_peak_info.get('double_peak_ratio', 0)
            
            # 设置标题和标签
            title_color = 'red' if has_double_peak else 'green'
            status = "有双峰问题" if has_double_peak else "无双峰问题"
            selected_mark = " ★选中★" if ch_name == best_channel else ""
            
            ax.set_title(f'{ch_name}{selected_mark} - {status} (比例: {double_peak_ratio:.2f})', 
                        color=title_color, fontsize=12)
            ax.set_ylabel('幅值', fontsize=10)
            ax.grid(True, alpha=0.3)
            
            # 添加质量信息
            quality_score = quality_info.get('quality_score', 0)
            r_peaks_count = quality_info.get('r_peaks_count', 0)
            ax.text(0.02, 0.98, f'质量分数: {quality_score:.3f}\nR峰数: {r_peaks_count}', 
                   transform=ax.transAxes, va='top', fontsize=9,
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 设置最后一个子图的X轴标签
        axes[-1].set_xlabel('时间 (秒)', fontsize=12)
        
        plt.suptitle(f'被试 {subject_id:02d} - ECG通道双峰问题检测对比', fontsize=14)
        plt.tight_layout()
        
        # 保存图片
        filename = f"{subject_id:02d}_double_peak_detection_comparison.png"
        filepath = os.path.join(PLOTS_DIR, filename)
        plt.savefig(filepath, dpi=100, bbox_inches='tight', facecolor='white')
        plt.close()
        
        logger.info(f"保存双峰检测对比图: {filename}")
        
    except Exception as e:
        logger.error(f"创建双峰对比图失败: {str(e)}")

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始测试双峰问题检测和修复")
    
    # 测试被试（包括有双峰问题的15号被试）
    test_subjects = [15, 1, 30]
    
    results = []
    for subject_id in test_subjects:
        result = test_double_peak_detection(subject_id, logger)
        if result:
            results.append(result)
        
        # 等待一下，避免过快处理
        time.sleep(2)
    
    # 总结报告
    logger.info("\n=== 双峰问题检测测试总结 ===")
    
    for result in results:
        subject_id = result['subject_id']
        best_channel = result['best_channel']
        quality_report = result['quality_report']
        
        logger.info(f"\n被试 {subject_id:02d}:")
        logger.info(f"  选择的最佳通道: {best_channel}")
        
        # 统计双峰问题
        channels_with_double_peak = []
        channels_without_double_peak = []
        
        for ch_name, quality_info in quality_report.items():
            if 'error' not in quality_info:
                double_peak_info = quality_info.get('double_peak_info', {})
                if double_peak_info.get('has_double_peak_issue', False):
                    channels_with_double_peak.append(ch_name)
                else:
                    channels_without_double_peak.append(ch_name)
        
        logger.info(f"  有双峰问题的通道: {channels_with_double_peak}")
        logger.info(f"  无双峰问题的通道: {channels_without_double_peak}")
        
        # 检查是否成功避免了双峰问题
        best_channel_info = quality_report.get(best_channel, {})
        best_has_double_peak = best_channel_info.get('double_peak_info', {}).get('has_double_peak_issue', False)
        
        if best_has_double_peak:
            logger.warning(f"  ⚠️ 选择的最佳通道仍有双峰问题")
        else:
            logger.info(f"  ✅ 成功选择了无双峰问题的通道")
    
    logger.info("双峰问题检测测试完成!")

if __name__ == "__main__":
    main()
