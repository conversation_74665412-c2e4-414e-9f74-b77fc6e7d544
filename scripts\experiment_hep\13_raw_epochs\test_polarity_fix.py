#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试极性标准化修复效果
使用修复后的HEP提取脚本处理几个被试，验证R波极性一致性
"""

import sys
import os
import argparse
import logging
import time
import warnings
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import mne
import neurokit2 as nk

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入修复后的HEP提取模块
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__))))
import importlib.util
spec = importlib.util.spec_from_file_location("hep_extraction", "01_hep_extraction.py")
hep_extraction = importlib.util.module_from_spec(spec)
spec.loader.exec_module(hep_extraction)

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']

plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

def setup_logging():
    """设置日志系统"""
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    logging.basicConfig(level=logging.INFO, format=log_format)
    return logging.getLogger(__name__)

def test_polarity_fix_single_subject(subject_id, logger):
    """测试单个被试的极性修复效果"""
    try:
        logger.info(f"=== 测试被试 {subject_id:02d} 极性修复效果 ===")
        
        # 使用修复后的处理函数
        result = hep_extraction.process_single_file_optimized(subject_id, '02', 'test', logger)
        
        if result:
            quality_report = result['quality_report']
            
            # 检查极性标准化信息
            r_peak_detection = quality_report.get('r_peak_detection', {})
            polarity_standardized = r_peak_detection.get('polarity_standardized', False)
            final_polarity = r_peak_detection.get('final_polarity', 'unknown')
            
            logger.info(f"被试 {subject_id:02d} 处理结果:")
            logger.info(f"  极性标准化: {polarity_standardized}")
            logger.info(f"  最终极性: {final_polarity}")
            logger.info(f"  ECG通道: {quality_report.get('ecg_channel_selection', {}).get('selected_channel', 'unknown')}")
            logger.info(f"  R峰检测方法: {r_peak_detection.get('method', 'unknown')}")
            logger.info(f"  最终epochs数量: {quality_report.get('final_epochs_count', 0)}")
            
            return {
                'subject_id': subject_id,
                'success': True,
                'polarity_standardized': polarity_standardized,
                'final_polarity': final_polarity,
                'ecg_channel': quality_report.get('ecg_channel_selection', {}).get('selected_channel', 'unknown'),
                'r_peak_method': r_peak_detection.get('method', 'unknown'),
                'epochs_count': quality_report.get('final_epochs_count', 0)
            }
        else:
            logger.error(f"被试 {subject_id:02d} 处理失败")
            return {
                'subject_id': subject_id,
                'success': False,
                'error': 'Processing failed'
            }
            
    except Exception as e:
        logger.error(f"测试被试 {subject_id:02d} 失败: {str(e)}")
        return {
            'subject_id': subject_id,
            'success': False,
            'error': str(e)
        }

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始测试极性标准化修复效果")
    
    # 测试被试（选择几个代表性的）
    test_subjects = [1, 15, 30]
    
    results = []
    for subject_id in test_subjects:
        result = test_polarity_fix_single_subject(subject_id, logger)
        results.append(result)
        
        # 等待一下，避免过快处理
        time.sleep(2)
    
    # 总结报告
    logger.info("\n=== 极性标准化修复测试总结 ===")
    successful_subjects = [r for r in results if r['success']]
    failed_subjects = [r for r in results if not r['success']]
    
    logger.info(f"测试被试数量: {len(results)}")
    logger.info(f"成功处理: {len(successful_subjects)}")
    logger.info(f"处理失败: {len(failed_subjects)}")
    
    if successful_subjects:
        logger.info("\n成功处理的被试:")
        for result in successful_subjects:
            logger.info(f"  被试 {result['subject_id']:02d}: "
                       f"极性标准化={result['polarity_standardized']}, "
                       f"最终极性={result['final_polarity']}, "
                       f"ECG通道={result['ecg_channel']}, "
                       f"epochs={result['epochs_count']}")
        
        # 检查极性一致性
        final_polarities = [r['final_polarity'] for r in successful_subjects]
        polarity_consistent = len(set(final_polarities)) == 1
        standardized_count = sum(1 for r in successful_subjects if r['polarity_standardized'])
        
        logger.info(f"\n极性一致性检查:")
        logger.info(f"  所有被试最终极性一致: {polarity_consistent}")
        logger.info(f"  需要极性标准化的被试数量: {standardized_count}")
        logger.info(f"  最终极性分布: {set(final_polarities)}")
        
        if polarity_consistent and 'positive' in final_polarities:
            logger.info("✅ 极性标准化修复成功！所有被试R波均为正向")
        else:
            logger.warning("⚠️ 极性标准化可能存在问题")
    
    if failed_subjects:
        logger.info("\n处理失败的被试:")
        for result in failed_subjects:
            logger.info(f"  被试 {result['subject_id']:02d}: {result.get('error', 'Unknown error')}")
    
    logger.info("极性标准化修复测试完成!")

if __name__ == "__main__":
    main()
