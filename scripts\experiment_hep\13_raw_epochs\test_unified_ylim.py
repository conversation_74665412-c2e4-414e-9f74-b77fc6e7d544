#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一Y轴范围修复效果
专门测试01号和30号被试的test_02阶段
"""

import sys
import os
import argparse
import logging
import time
import warnings
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import mne
import neurokit2 as nk
import h5py
from scipy import signal

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']

plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

# 数据路径配置
DATA_DIR = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-HBA\result\hep_analysis\14_rest_vs_test_analysis\hepdata"
PLOTS_DIR = os.path.join(OUTPUT_DIR, 'plots')
os.makedirs(PLOTS_DIR, exist_ok=True)

# 参数配置
SAMPLING_RATE = 500
HEP_TMIN = -0.5
HEP_TMAX = 1.0
VIS_TMIN = -0.2
VIS_TMAX = 0.65
BASELINE_TMIN = -0.2
BASELINE_TMAX = 0.0
FILTER_LOW = 0.1
FILTER_HIGH = 30.0

# 电极组合定义
CENTRAL_ELECTRODES = ['F1', 'F2', 'FC1', 'FC2', 'C1', 'Cz', 'C2', 'Fz']
RIGHT_HEMISPHERE = ['F2', 'F4', 'F6', 'AF4', 'AF8', 'FP2', 'FC2', 'FC4', 'FC6']
LEFT_HEMISPHERE = ['F1', 'F3', 'F5', 'AF3', 'AF7', 'FP1', 'FC1', 'FC3', 'FC5']

# ECG导联候选列表
ECG_CANDIDATES = ['ECG11', 'ECG7', 'ECG8', 'ECG12']

def setup_logging():
    """设置日志系统"""
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    logging.basicConfig(level=logging.INFO, format=log_format)
    return logging.getLogger(__name__)

def load_data(subject_id, stage_number, stage_type, logger):
    """加载指定被试和阶段的数据"""
    try:
        filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_{stage_type}_TP9TP10Ref.fif"
        file_path = os.path.join(DATA_DIR, filename)

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {filename}")
            return None

        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
        return raw

    except Exception as e:
        logger.error(f"加载数据失败 {filename}: {str(e)}")
        return None

def select_best_ecg_channel(raw, logger=None):
    """简化的ECG通道选择"""
    available_ecg = [ch for ch in ECG_CANDIDATES if ch in raw.ch_names]
    if not available_ecg:
        available_ecg = [ch for ch in raw.ch_names if ch.startswith('ECG')]
    
    if not available_ecg:
        raise ValueError("未找到任何ECG通道")
    
    # 简单选择第一个可用通道
    best_channel = available_ecg[0]
    ecg_data = raw.get_data(picks=[best_channel])[0]
    
    if logger:
        logger.info(f"选择ECG通道: {best_channel}")
    
    return best_channel, ecg_data

def detect_r_peaks_simple(ecg_signal, sampling_rate, logger=None):
    """简化的R峰检测"""
    try:
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=sampling_rate)
        _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=sampling_rate)
        r_peaks = rpeaks_info.get('ECG_R_Peaks', [])
        
        if logger:
            logger.info(f"检测到 {len(r_peaks)} 个R峰")
        
        return r_peaks
    except Exception as e:
        if logger:
            logger.error(f"R峰检测失败: {str(e)}")
        return np.array([])

def extract_hep_epochs(raw, r_peaks, logger=None):
    """提取HEP epochs"""
    try:
        # 应用滤波
        raw_filtered = raw.copy()
        raw_filtered.filter(l_freq=FILTER_LOW, h_freq=FILTER_HIGH, fir_design='firwin', verbose=False)
        
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros(len(r_peaks), dtype=int), np.ones(len(r_peaks), dtype=int)])
        
        # 提取epochs
        epochs = mne.Epochs(raw_filtered, events, event_id={'heartbeat': 1},
                           tmin=HEP_TMIN, tmax=HEP_TMAX,
                           baseline=(BASELINE_TMIN, BASELINE_TMAX),
                           preload=True, verbose=False)
        
        if logger:
            logger.info(f"提取到 {len(epochs)} 个HEP epochs")
        
        return epochs
    except Exception as e:
        if logger:
            logger.error(f"提取HEP epochs失败: {str(e)}")
        return None

def test_unified_ylim_visualization(subject_id, logger):
    """测试统一Y轴范围的可视化"""
    try:
        logger.info(f"开始处理被试 {subject_id:02d}")
        
        # 加载数据
        raw = load_data(subject_id, '02', 'test', logger)
        if raw is None:
            return False
        
        # 选择ECG通道
        ecg_channel, ecg_signal = select_best_ecg_channel(raw, logger)
        
        # 检测R峰
        r_peaks = detect_r_peaks_simple(ecg_signal, raw.info['sfreq'], logger)
        if len(r_peaks) < 10:
            logger.warning(f"被试 {subject_id:02d} R峰数量不足")
            return False
        
        # 提取HEP epochs
        epochs = extract_hep_epochs(raw, r_peaks, logger)
        if epochs is None:
            return False
        
        # 创建可视化
        create_unified_ylim_visualization(epochs, subject_id, ecg_channel, logger)
        
        return True
        
    except Exception as e:
        logger.error(f"处理被试 {subject_id:02d} 失败: {str(e)}")
        return False

def create_unified_ylim_visualization(epochs, subject_id, ecg_channel, logger):
    """创建统一Y轴范围的可视化"""
    try:
        # 设置图形尺寸（2:1比例）
        subplot_width = 6
        subplot_height = 3
        total_width = subplot_width * 3 + 1
        total_height = subplot_height + 2
        
        fig = plt.figure(figsize=(total_width, total_height))
        
        # 创建子图
        axes = []
        for i in range(3):
            ax = fig.add_subplot(1, 3, i+1)
            axes.append(ax)
        
        fig.suptitle(f'Subject {subject_id:02d} - test_02 HEP Average - 统一Y轴范围测试\n'
                    f'ECG: {ecg_channel}', fontsize=14, color='blue')
        
        # 获取时间轴和数据
        times_ms = epochs.times * 1000
        vis_mask = (times_ms >= VIS_TMIN * 1000) & (times_ms <= VIS_TMAX * 1000)
        vis_times = times_ms[vis_mask]
        epochs_data_uv = epochs.get_data() * 1e6
        ch_names = epochs.ch_names
        
        # 定义电极组合
        electrode_groups = [
            (CENTRAL_ELECTRODES, 'Central', 'red'),
            (LEFT_HEMISPHERE, 'Left Hemisphere', 'blue'),
            (RIGHT_HEMISPHERE, 'Right Hemisphere', 'green')
        ]
        
        # 第一步：计算所有电极组的数据，确定统一的Y轴范围
        all_vis_data = []
        group_vis_data = []
        
        for electrodes, title, color in electrode_groups:
            electrode_indices = []
            for electrode in electrodes:
                if electrode in ch_names:
                    electrode_indices.append(ch_names.index(electrode))
            
            if electrode_indices:
                group_data = epochs_data_uv[:, electrode_indices, :]
                electrode_avg = np.mean(group_data, axis=1)
                avg_data = np.mean(electrode_avg, axis=0)
                vis_avg_data = avg_data[vis_mask]
                
                group_vis_data.append((vis_avg_data, title, color, len(electrode_indices)))
                all_vis_data.extend(vis_avg_data)
                
                logger.info(f"{title} 数据范围: {np.min(vis_avg_data):.2f} 到 {np.max(vis_avg_data):.2f} μV")
            else:
                group_vis_data.append((None, title, color, 0))
        
        # 计算统一的Y轴范围
        if all_vis_data:
            global_min = np.min(all_vis_data)
            global_max = np.max(all_vis_data)
            global_range = global_max - global_min
            y_margin = max(global_range * 0.15, 2.0)
            unified_y_min = global_min - y_margin
            unified_y_max = global_max + y_margin
            
            logger.info(f"统一Y轴范围: {unified_y_min:.2f} 到 {unified_y_max:.2f} μV")
        else:
            unified_y_min, unified_y_max = -10, 10
        
        # 绘制所有子图
        for i, (vis_avg_data, title, color, electrode_count) in enumerate(group_vis_data):
            ax = axes[i]
            
            if vis_avg_data is not None:
                ax.plot(vis_times, vis_avg_data, color=color, linewidth=2,
                       label=f'{title} (n={electrode_count})')
                
                ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
                ax.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='R-peak')
                ax.axvspan(BASELINE_TMIN*1000, BASELINE_TMAX*1000, alpha=0.2, color='gray', label='Baseline')
                
                ax.set_xlabel('Time (ms)', fontsize=12)
                ax.set_ylabel('Amplitude (μV)', fontsize=12)
                ax.set_title(title, fontsize=14)
                ax.grid(True, alpha=0.3)
                ax.legend(fontsize=10)
                
                ax.set_xlim(VIS_TMIN * 1000, VIS_TMAX * 1000)
                ax.set_ylim(unified_y_min, unified_y_max)  # 统一Y轴范围
                
                ax.tick_params(axis='both', which='major', labelsize=11)
            else:
                ax.text(0.5, 0.5, f'No {title} electrodes', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{title} (No Data)')
                ax.set_xlim(VIS_TMIN * 1000, VIS_TMAX * 1000)
                ax.set_ylim(unified_y_min, unified_y_max)
        
        plt.tight_layout()
        
        # 保存图片
        filename = f"{subject_id:02d}_test_02_hep_unified_ylim_test.png"
        filepath = os.path.join(PLOTS_DIR, filename)
        plt.savefig(filepath, dpi=100, bbox_inches='tight', facecolor='white')
        plt.close()
        
        logger.info(f"保存测试图片: {filename}")
        
    except Exception as e:
        logger.error(f"创建可视化失败: {str(e)}")

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始统一Y轴范围测试")
    
    # 测试被试
    test_subjects = [1, 30]
    
    for subject_id in test_subjects:
        success = test_unified_ylim_visualization(subject_id, logger)
        if success:
            logger.info(f"被试 {subject_id:02d} 测试成功")
        else:
            logger.error(f"被试 {subject_id:02d} 测试失败")
    
    logger.info("统一Y轴范围测试完成!")

if __name__ == "__main__":
    main()
