#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试R波极性标准化功能
验证所有被试的R波方向是否一致
"""

import sys
import os
import argparse
import logging
import time
import warnings
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import mne
import neurokit2 as nk
import h5py
from scipy import signal

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']

plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

# 数据路径配置
DATA_DIR = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-HBA\result\hep_analysis\14_rest_vs_test_analysis\hepdata"
PLOTS_DIR = os.path.join(OUTPUT_DIR, 'plots')
os.makedirs(PLOTS_DIR, exist_ok=True)

# 参数配置
SAMPLING_RATE = 500
HEP_TMIN = -0.5
HEP_TMAX = 1.0
VIS_TMIN = -0.2
VIS_TMAX = 0.65
BASELINE_TMIN = -0.2
BASELINE_TMAX = 0.0
FILTER_LOW = 0.1
FILTER_HIGH = 30.0

# 电极组合定义
CENTRAL_ELECTRODES = ['F1', 'F2', 'FC1', 'FC2', 'C1', 'Cz', 'C2', 'Fz']
RIGHT_HEMISPHERE = ['F2', 'F4', 'F6', 'AF4', 'AF8', 'FP2', 'FC2', 'FC4', 'FC6']
LEFT_HEMISPHERE = ['F1', 'F3', 'F5', 'AF3', 'AF7', 'FP1', 'FC1', 'FC3', 'FC5']

# ECG导联候选列表
ECG_CANDIDATES = ['ECG11', 'ECG7', 'ECG8', 'ECG12']

def setup_logging():
    """设置日志系统"""
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    logging.basicConfig(level=logging.INFO, format=log_format)
    return logging.getLogger(__name__)

def load_data(subject_id, stage_number, stage_type, logger):
    """加载指定被试和阶段的数据"""
    try:
        filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_{stage_type}_TP9TP10Ref.fif"
        file_path = os.path.join(DATA_DIR, filename)

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {filename}")
            return None

        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
        return raw

    except Exception as e:
        logger.error(f"加载数据失败 {filename}: {str(e)}")
        return None

def detect_ecg_polarity(ecg_signal, r_peaks, sampling_rate, window_ms=100):
    """检测ECG信号的R波极性"""
    window_samples = int(window_ms * sampling_rate / 1000)
    r_wave_amplitudes = []

    for peak in r_peaks[:min(10, len(r_peaks))]:  # 只分析前10个R峰
        start = max(0, peak - window_samples // 2)
        end = min(len(ecg_signal), peak + window_samples // 2)

        if start < end:
            local_signal = ecg_signal[start:end]
            baseline = np.median(local_signal)
            peak_value = ecg_signal[peak]
            r_wave_amplitudes.append(peak_value - baseline)

    if r_wave_amplitudes:
        avg_amplitude = np.mean(r_wave_amplitudes)
        return 'positive' if avg_amplitude > 0 else 'negative'
    else:
        return 'positive'

def standardize_ecg_polarity(ecg_signal, r_peaks, sampling_rate, target_polarity='positive', logger=None):
    """标准化ECG信号极性"""
    try:
        current_polarity = detect_ecg_polarity(ecg_signal, r_peaks, sampling_rate)

        if current_polarity == target_polarity:
            if logger:
                logger.info(f"ECG极性已为{target_polarity}，无需调整")
            return ecg_signal, False
        else:
            standardized_signal = -ecg_signal
            if logger:
                logger.info(f"ECG极性从{current_polarity}翻转为{target_polarity}")
            return standardized_signal, True

    except Exception as e:
        if logger:
            logger.warning(f"极性标准化失败: {str(e)}，使用原始信号")
        return ecg_signal, False

def analyze_all_ecg_channels(raw, logger=None):
    """分析所有ECG通道的极性"""
    available_ecg = [ch for ch in ECG_CANDIDATES if ch in raw.ch_names]
    if not available_ecg:
        available_ecg = [ch for ch in raw.ch_names if ch.startswith('ECG')]

    if not available_ecg:
        raise ValueError("未找到任何ECG通道")

    channel_analysis = {}

    for ch_name in available_ecg:
        try:
            ecg_data = raw.get_data(picks=[ch_name])[0]
            ecg_cleaned = nk.ecg_clean(ecg_data, sampling_rate=raw.info['sfreq'])
            _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=raw.info['sfreq'])
            r_peaks = rpeaks_info.get('ECG_R_Peaks', [])

            if len(r_peaks) > 5:
                polarity = detect_ecg_polarity(ecg_cleaned, r_peaks, raw.info['sfreq'])
                channel_analysis[ch_name] = {
                    'polarity': polarity,
                    'r_peaks_count': len(r_peaks),
                    'signal': ecg_cleaned,
                    'r_peaks': r_peaks
                }
                if logger:
                    logger.info(f"{ch_name}: 极性={polarity}, R峰数={len(r_peaks)}")
            else:
                channel_analysis[ch_name] = {
                    'polarity': 'unknown',
                    'r_peaks_count': len(r_peaks),
                    'signal': ecg_cleaned,
                    'r_peaks': r_peaks
                }
                if logger:
                    logger.warning(f"{ch_name}: R峰数量不足 ({len(r_peaks)})")

        except Exception as e:
            if logger:
                logger.error(f"分析{ch_name}失败: {str(e)}")
            channel_analysis[ch_name] = {'error': str(e)}

    return channel_analysis

def select_best_ecg_channel(raw, logger=None):
    """选择最佳ECG通道"""
    available_ecg = [ch for ch in ECG_CANDIDATES if ch in raw.ch_names]
    if not available_ecg:
        available_ecg = [ch for ch in raw.ch_names if ch.startswith('ECG')]

    if not available_ecg:
        raise ValueError("未找到任何ECG通道")

    # 简单选择第一个可用通道
    best_channel = available_ecg[0]
    ecg_data = raw.get_data(picks=[best_channel])[0]

    if logger:
        logger.info(f"选择ECG通道: {best_channel}")

    return best_channel, ecg_data

def detect_r_peaks_simple(ecg_signal, sampling_rate, logger=None):
    """简化的R峰检测"""
    try:
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=sampling_rate)
        _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=sampling_rate)
        r_peaks = rpeaks_info.get('ECG_R_Peaks', [])

        if logger:
            logger.info(f"检测到 {len(r_peaks)} 个R峰")

        return r_peaks, ecg_cleaned
    except Exception as e:
        if logger:
            logger.error(f"R峰检测失败: {str(e)}")
        return np.array([]), ecg_signal

def test_polarity_standardization(subject_id, logger):
    """测试单个被试的极性标准化"""
    try:
        logger.info(f"开始测试被试 {subject_id:02d} 的极性标准化")

        # 加载数据
        raw = load_data(subject_id, '02', 'test', logger)
        if raw is None:
            return None

        # 分析所有ECG通道的极性
        logger.info(f"=== 被试 {subject_id:02d} 所有ECG通道极性分析 ===")
        channel_analysis = analyze_all_ecg_channels(raw, logger)

        # 找到有负向极性的通道
        negative_channels = [ch for ch, info in channel_analysis.items()
                           if info.get('polarity') == 'negative']
        positive_channels = [ch for ch, info in channel_analysis.items()
                           if info.get('polarity') == 'positive']

        logger.info(f"正向极性通道: {positive_channels}")
        logger.info(f"负向极性通道: {negative_channels}")

        # 选择一个有代表性的通道进行测试
        if negative_channels:
            # 如果有负向通道，选择第一个负向通道
            test_channel = negative_channels[0]
            logger.info(f"选择负向通道进行测试: {test_channel}")
        else:
            # 否则选择第一个正向通道
            test_channel = positive_channels[0] if positive_channels else list(channel_analysis.keys())[0]
            logger.info(f"选择正向通道进行测试: {test_channel}")

        # 获取测试通道的数据
        channel_info = channel_analysis[test_channel]
        if 'error' in channel_info:
            logger.error(f"通道 {test_channel} 分析失败: {channel_info['error']}")
            return None

        ecg_cleaned = channel_info['signal']
        r_peaks = channel_info['r_peaks']
        original_polarity = channel_info['polarity']

        if len(r_peaks) < 10:
            logger.warning(f"被试 {subject_id:02d} 通道 {test_channel} R峰数量不足")
            return None

        # 标准化极性
        standardized_signal, polarity_flipped = standardize_ecg_polarity(
            ecg_cleaned, r_peaks, raw.info['sfreq'], 'positive', logger)

        # 验证标准化后的极性
        final_polarity = detect_ecg_polarity(standardized_signal, r_peaks, raw.info['sfreq'])

        # 创建对比可视化
        create_polarity_comparison_plot(subject_id, ecg_cleaned, standardized_signal, r_peaks,
                                      original_polarity, final_polarity, polarity_flipped, logger)

        result = {
            'subject_id': subject_id,
            'ecg_channel': test_channel,
            'all_channels_analysis': channel_analysis,
            'original_polarity': original_polarity,
            'final_polarity': final_polarity,
            'polarity_flipped': polarity_flipped,
            'r_peaks_count': len(r_peaks),
            'negative_channels': negative_channels,
            'positive_channels': positive_channels
        }

        return result

    except Exception as e:
        logger.error(f"测试被试 {subject_id:02d} 失败: {str(e)}")
        return None

def create_polarity_comparison_plot(subject_id, original_signal, standardized_signal, r_peaks,
                                  original_polarity, final_polarity, polarity_flipped, logger):
    """创建极性对比图"""
    try:
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 8))

        # 选择显示的时间段（前5秒）
        display_samples = min(2500, len(original_signal))  # 5秒 * 500Hz
        times = np.arange(display_samples) / 500.0  # 转换为秒

        # 找到显示范围内的R峰
        display_r_peaks = r_peaks[r_peaks < display_samples]

        # 原始信号
        ax1.plot(times, original_signal[:display_samples], 'b-', linewidth=1, label='原始ECG信号')
        ax1.scatter(display_r_peaks/500.0, original_signal[display_r_peaks],
                   color='red', s=50, zorder=5, label=f'R峰 ({original_polarity})')
        ax1.set_title(f'被试 {subject_id:02d} - 原始ECG信号 (极性: {original_polarity})', fontsize=14)
        ax1.set_ylabel('幅值 (μV)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # 标准化后信号
        ax2.plot(times, standardized_signal[:display_samples], 'g-', linewidth=1, label='标准化ECG信号')
        ax2.scatter(display_r_peaks/500.0, standardized_signal[display_r_peaks],
                   color='red', s=50, zorder=5, label=f'R峰 ({final_polarity})')

        flip_status = "已翻转" if polarity_flipped else "未翻转"
        ax2.set_title(f'被试 {subject_id:02d} - 标准化ECG信号 (极性: {final_polarity}, {flip_status})', fontsize=14)
        ax2.set_xlabel('时间 (秒)', fontsize=12)
        ax2.set_ylabel('幅值 (μV)', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        plt.tight_layout()

        # 保存图片
        filename = f"{subject_id:02d}_polarity_standardization_test.png"
        filepath = os.path.join(PLOTS_DIR, filename)
        plt.savefig(filepath, dpi=100, bbox_inches='tight', facecolor='white')
        plt.close()

        logger.info(f"保存极性测试图片: {filename}")

    except Exception as e:
        logger.error(f"创建极性对比图失败: {str(e)}")

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始R波极性标准化测试")

    # 测试被试（选择几个代表性的）
    test_subjects = [1, 15, 30, 5, 20]

    results = []
    for subject_id in test_subjects:
        result = test_polarity_standardization(subject_id, logger)
        if result:
            results.append(result)
            logger.info(f"被试 {subject_id:02d}: {result['original_polarity']} -> {result['final_polarity']}, "
                       f"翻转: {result['polarity_flipped']}")

    # 总结报告
    logger.info("\n=== 极性标准化测试总结 ===")
    positive_count = sum(1 for r in results if r['original_polarity'] == 'positive')
    negative_count = sum(1 for r in results if r['original_polarity'] == 'negative')
    flipped_count = sum(1 for r in results if r['polarity_flipped'])

    logger.info(f"测试被试数量: {len(results)}")
    logger.info(f"原始正向极性: {positive_count}")
    logger.info(f"原始负向极性: {negative_count}")
    logger.info(f"需要翻转极性: {flipped_count}")
    logger.info(f"最终全部标准化为正向极性: {all(r['final_polarity'] == 'positive' for r in results)}")

    logger.info("R波极性标准化测试完成!")

if __name__ == "__main__":
    main()
