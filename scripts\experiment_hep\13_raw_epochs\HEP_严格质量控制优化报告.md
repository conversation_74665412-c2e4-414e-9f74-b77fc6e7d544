# HEP提取流程严格质量控制优化报告

## 概述

本报告详细说明了对HEP（心跳诱发电位）提取流程的全面质量控制优化，旨在解决当前存在的数据质量问题，特别是针对30号被试的R波识别偏差等问题。

## 主要优化内容

### 1. 严格的伪迹处理优化

#### 问题分析
- 原始阈值设置过于宽松（5000μV），无法有效去除伪迹
- 缺乏精细的插值方法处理伪迹段

#### 优化方案
- **降低伪迹检测阈值**：从5000μV降低到100μV
- **精细插值方法**：使用三次样条插值替换伪迹段
- **自适应处理**：确保至少30%数据干净才进行插值
- **分层处理**：针对不同被试使用不同阈值（如30号被试使用75μV）

#### 技术实现
```python
# 严格伪迹去除参数
QUALITY_CONTROL = {
    'artifact_threshold': 100.0,  # μV（严格）
    # 其他参数...
}
```

### 2. 增强的EEG预处理流程

#### 问题分析
- 缺少系统性伪迹去除技术
- 未使用ICA等高级预处理方法

#### 优化方案
- **ICA伪迹去除**：自动检测和去除眼电、肌电伪迹
- **高斯平滑**：σ=1.5的高斯滤波器减少噪声
- **增强滤波**：0.5-45Hz带通滤波（更严格范围）
- **多层预处理**：严格伪迹去除 → 滤波 → ICA → 平滑

#### 技术实现
```python
def apply_enhanced_preprocessing(raw, logger=None):
    # 1. 严格伪迹去除
    # 2. 滤波
    # 3. ICA去除系统性伪迹
    # 4. 高斯平滑
```

### 3. 严格的生理学标准

#### 问题分析
- 质量控制标准被过度放宽
- 不符合严格的生理学要求

#### 优化方案
- **基线稳定性**：<30μV标准差（严格）
- **HEP成分变化**：≥2.0倍基线标准差（严格）
- **R波对齐精度**：±5个采样点（±10ms）（严格）
- **信噪比要求**：≥1.5（严格）
- **最小R峰数量**：≥50个（严格）

#### 技术实现
```python
QUALITY_CONTROL = {
    'baseline_std_threshold': 30.0,      # μV（严格）
    'hep_change_min_ratio': 2.0,         # 严格
    'r_peak_alignment_tolerance': 5,     # 采样点（严格）
    'snr_threshold': 1.5,                # 严格
    'min_r_peaks': 50,                   # 严格
}
```

### 4. 特殊处理策略

#### 30号被试专门优化
- **问题**：R波识别偏差20个采样点
- **解决方案**：
  - 特殊R波检测算法顺序
  - 专门的ECG导联选择策略
  - R波位置手动校正算法
  - 更严格的伪迹阈值（75μV）

#### 技术实现
```python
PROBLEMATIC_SUBJECTS = {
    30: {
        'r_peak_methods': ['hamilton2002', 'engzeemod2012', 'pantompkins1985'],
        'ecg_candidates': ['ECG8', 'ECG12', 'ECG7', 'ECG11'],
        'artifact_threshold': 75.0,
        'manual_r_peak_correction': True,
        'description': 'R波识别偏差20个采样点问题'
    }
}
```

### 5. 系统化验证机制

#### 自动质量评估
- **多维度验证**：基线稳定性、HEP成分、信噪比、R波对齐
- **严格标准**：绝不放宽验证标准
- **分级标记**：通过/失败明确标记

#### 视觉验证增强
- **质量评估图片**：包含所有质量指标的可视化
- **状态颜色编码**：绿色（通过）/红色（失败）
- **详细质量信息**：每个指标的具体数值和标准对比

## 质量控制原则

### 核心原则
1. **绝不放宽验证标准**：所有不符合严格生理学标准的数据都标记为质量问题
2. **改善预处理技术**：通过技术手段解决质量问题，而非降低标准
3. **系统化验证**：每个处理步骤都有相应的质量检查
4. **特殊情况特殊处理**：针对已知问题被试使用专门算法

### 验证流程
1. **预处理质量检查**：伪迹去除效果验证
2. **R波检测质量检查**：对齐精度和检测准确性
3. **HEP信号质量检查**：生理学合理性验证
4. **最终质量评估**：综合所有指标的整体评估

## 技术参数总结

### 滤波参数
- **频率范围**：0.5-45Hz（更严格）
- **高斯平滑**：σ=1.5
- **基线校正**：-200ms到0ms

### 质量控制阈值
- **伪迹阈值**：100μV（标准）/ 75μV（30号被试）
- **基线稳定性**：<30μV
- **HEP变化要求**：≥2.0倍基线
- **R波对齐精度**：±10ms
- **信噪比要求**：≥1.5

### 数据窗口
- **提取窗口**：-500ms到+1000ms
- **可视化窗口**：-200ms到+650ms
- **HEP分析窗口**：200ms到600ms

## 预期效果

### 数据质量提升
1. **伪迹污染显著减少**：100μV阈值有效去除大部分伪迹
2. **基线稳定性改善**：ICA和严格预处理提升信号质量
3. **HEP成分清晰度提高**：更好的信噪比和成分分离

### 特殊问题解决
1. **30号被试R波偏差**：专门算法校正20个采样点偏差
2. **系统性伪迹去除**：ICA有效处理眼电、肌电干扰
3. **个体化处理策略**：针对不同被试的特殊问题

### 验证机制完善
1. **自动化质量评估**：每个文件都有详细质量报告
2. **可视化验证**：直观的质量评估图片
3. **标准化流程**：统一的质量控制标准和流程

## 使用说明

### 运行主脚本
```bash
python 01_hep_extraction.py --background
```

### 测试质量控制
```bash
python test_strict_quality_control.py
```

### 查看质量报告
- 质量报告保存在：`result/hep_analysis/14_rest_vs_test_analysis/hepdata/quality_reports/`
- 可视化图片保存在：`result/hep_analysis/14_rest_vs_test_analysis/hepdata/plots/`

## 注意事项

1. **处理时间增加**：由于增加了ICA等复杂处理，单个文件处理时间可能增加
2. **内存需求**：ICA处理需要更多内存，建议在高配置机器上运行
3. **质量标记**：不通过验证的数据仍会保存，但会明确标记质量状态
4. **特殊被试**：30号被试等问题被试会自动应用特殊处理策略

## 后续建议

1. **持续监控**：定期检查质量报告，识别新的问题模式
2. **参数调优**：根据实际效果调整质量控制参数
3. **扩展特殊处理**：为其他问题被试开发专门的处理策略
4. **验证标准更新**：根据最新研究更新生理学验证标准
