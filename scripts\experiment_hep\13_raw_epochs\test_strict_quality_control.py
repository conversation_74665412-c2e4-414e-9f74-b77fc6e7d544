#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP严格质量控制测试脚本

测试新的严格质量控制流程，特别针对：
1. 伪迹处理优化（100μV阈值）
2. EEG信号质量提升（ICA去除伪迹）
3. 严格生理学标准验证
4. 30号被试特殊处理
5. 系统化验证机制
"""

import os
import sys
import logging
import time

# 添加脚本路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主要的HEP提取模块
from hep_extraction import (
    process_single_file_optimized,
    setup_logging,
    PROBLEMATIC_SUBJECTS,
    QUALITY_CONTROL
)

def test_single_subject(subject_id, stage_type='test', stage_number='02'):
    """
    测试单个被试的HEP提取流程
    """
    logger = setup_logging(background_mode=False)
    
    logger.info(f"=== 测试被试 {subject_id:02d} - {stage_type}_{stage_number} ===")
    
    # 显示质量控制标准
    logger.info("严格质量控制标准:")
    for key, value in QUALITY_CONTROL.items():
        logger.info(f"  {key}: {value}")
    
    # 检查是否为问题被试
    if subject_id in PROBLEMATIC_SUBJECTS:
        logger.info(f"被试{subject_id}为问题被试: {PROBLEMATIC_SUBJECTS[subject_id]['description']}")
    
    # 执行处理
    start_time = time.time()
    result = process_single_file_optimized(subject_id, stage_number, stage_type, logger)
    processing_time = time.time() - start_time
    
    # 分析结果
    if result:
        quality_report = result['quality_report']
        logger.info(f"处理成功! 用时: {processing_time:.1f}秒")
        
        # 显示质量评估结果
        logger.info("=== 质量评估结果 ===")
        epochs_quality = quality_report.get('epochs_quality', {})
        logger.info(f"验证状态: {epochs_quality.get('validation_status', 'unknown')}")
        logger.info(f"基线稳定性: {epochs_quality.get('baseline_std', 0):.2f}μV")
        logger.info(f"HEP变化比例: {epochs_quality.get('hep_change_ratio', 0):.2f}")
        logger.info(f"信噪比: {epochs_quality.get('snr', 0):.2f}")
        logger.info(f"最大幅度: {epochs_quality.get('max_amplitude', 0):.1f}μV")
        
        # 显示R波对齐结果
        alignment_info = quality_report.get('r_peak_alignment', {})
        logger.info(f"R波对齐误差: {alignment_info.get('position_error_ms', 0):.2f}ms")
        logger.info(f"对齐验证: {'通过' if alignment_info.get('alignment_passed', False) else '失败'}")
        
        # 显示质量问题
        issues = epochs_quality.get('issues', [])
        if issues:
            logger.warning("检测到质量问题:")
            for issue in issues:
                logger.warning(f"  • {issue}")
        else:
            logger.info("✓ 所有质量检查通过")
        
        # 显示特殊处理信息
        special_processing = quality_report.get('special_processing', {})
        if special_processing.get('is_problematic_subject', False):
            logger.info(f"应用特殊处理: {special_processing.get('description', 'N/A')}")
        
        return True
    else:
        logger.error(f"处理失败! 用时: {processing_time:.1f}秒")
        return False

def test_problematic_subjects():
    """
    测试所有问题被试
    """
    logger = setup_logging(background_mode=False)
    
    logger.info("=== 测试所有问题被试 ===")
    
    results = {}
    for subject_id in PROBLEMATIC_SUBJECTS.keys():
        logger.info(f"\n测试问题被试 {subject_id}")
        success = test_single_subject(subject_id)
        results[subject_id] = success
    
    # 总结结果
    logger.info("\n=== 问题被试测试总结 ===")
    for subject_id, success in results.items():
        status = "成功" if success else "失败"
        logger.info(f"被试{subject_id}: {status}")
    
    success_rate = sum(results.values()) / len(results) * 100
    logger.info(f"问题被试处理成功率: {success_rate:.1f}%")

def test_normal_subjects():
    """
    测试正常被试（随机选择几个）
    """
    logger = setup_logging(background_mode=False)
    
    logger.info("=== 测试正常被试 ===")
    
    # 选择几个正常被试进行测试
    normal_subjects = [1, 5, 10, 15, 20]  # 排除问题被试
    
    results = {}
    for subject_id in normal_subjects:
        if subject_id not in PROBLEMATIC_SUBJECTS:
            logger.info(f"\n测试正常被试 {subject_id}")
            success = test_single_subject(subject_id)
            results[subject_id] = success
    
    # 总结结果
    logger.info("\n=== 正常被试测试总结 ===")
    for subject_id, success in results.items():
        status = "成功" if success else "失败"
        logger.info(f"被试{subject_id}: {status}")
    
    success_rate = sum(results.values()) / len(results) * 100
    logger.info(f"正常被试处理成功率: {success_rate:.1f}%")

def main():
    """
    主测试函数
    """
    logger = setup_logging(background_mode=False)
    
    logger.info("开始HEP严格质量控制测试")
    logger.info("=" * 60)
    
    # 测试30号被试（已知问题被试）
    logger.info("1. 测试30号被试（R波识别偏差问题）")
    test_single_subject(30)
    
    print("\n" + "=" * 60)
    
    # 测试一个正常被试
    logger.info("2. 测试正常被试（对比）")
    test_single_subject(1)
    
    print("\n" + "=" * 60)
    
    # 可选：测试所有问题被试
    user_input = input("是否测试所有问题被试？(y/n): ")
    if user_input.lower() == 'y':
        test_problematic_subjects()
    
    print("\n" + "=" * 60)
    
    # 可选：测试多个正常被试
    user_input = input("是否测试多个正常被试？(y/n): ")
    if user_input.lower() == 'y':
        test_normal_subjects()
    
    logger.info("HEP严格质量控制测试完成")

if __name__ == "__main__":
    main()
